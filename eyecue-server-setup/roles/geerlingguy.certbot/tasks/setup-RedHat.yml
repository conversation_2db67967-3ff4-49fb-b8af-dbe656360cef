---
# See: https://github.com/geerlingguy/ansible-role-certbot/issues/107
- block:

  - name: Ensure dnf-plugins are installed on CentOS 8+.
    yum:
      name: dnf-plugins-core
      state: present

  - block:

      - name: Enable DNF module for CentOS 8.3+.
        shell: |
          dnf config-manager --set-enabled powertools
        args:
          warn: false
        register: dnf_module_enable
        changed_when: false

        when: ansible_facts['distribution_version'] is version('8.3', '>=')

      - name: Enable DNF module for CentOS 8.0–8.2.
        shell: |
          dnf config-manager --set-enabled PowerTools
        args:
          warn: false
        register: dnf_module_enable
        changed_when: false

    when: ansible_facts['distribution_version'] is version('8.2', '<=')

  when:
    - ansible_distribution == 'CentOS'
    - ansible_distribution_major_version | int >= 8
