---
- name: <PERSON><PERSON> Ce<PERSON>bot into configured directory.
  git:
    repo: "{{ certbot_repo }}"
    dest: "{{ certbot_dir }}"
    version: "{{ certbot_version }}"
    update: "{{ certbot_keep_updated }}"
    force: true

- name: Set Certbot script variable.
  set_fact:
    certbot_script: "{{ certbot_dir }}/certbot-auto"

- name: Ensure certbot-auto is executable.
  file:
    path: "{{ certbot_script }}"
    mode: 0755
