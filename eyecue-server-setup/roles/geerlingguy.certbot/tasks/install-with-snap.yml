---
- name: Ensure snapd is installed.
  package:
    name: snapd
    state: present
  register: snapd_install

- name: Ensure snapd is enabled.
  systemd:
    name: snapd.socket
    enabled: true
    state: started

- name: Enable classic snap support.
  file:
    src: /var/lib/snapd/snap
    dest: /snap
    state: link
  when: ansible_os_family != "Debian"

- name: Update snap after install.
  shell: snap install core; snap refresh core
  changed_when: true
  failed_when: false
  when: snapd_install is changed

- name: Install certbot via snap.
  snap:
    name: certbot
    classic: true

- name: Symlink certbot into place.
  file:
    src: /snap/bin/certbot
    dest: /usr/bin/certbot
    state: link
  ignore_errors: "{{ ansible_check_mode }}"

- name: Set Certbot script variable.
  set_fact:
    certbot_script: /usr/bin/certbot
