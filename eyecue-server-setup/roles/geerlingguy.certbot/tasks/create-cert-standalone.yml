---
- name: Check if certificate already exists.
  stat:
    path: /etc/letsencrypt/live/{{ cert_item.domains | first | replace('*.', '') }}/cert.pem
  register: letsencrypt_cert

- name: Ensure pre and post hook folders exist.
  file:
    path: /etc/letsencrypt/renewal-hooks/{{ item }}
    state: directory
    mode: 0755
    owner: root
    group: root
  with_items:
    - pre
    - post

- name: Create pre hook to stop services.
  template:
    src: stop_services.j2
    dest: /etc/letsencrypt/renewal-hooks/pre/stop_services
    owner: root
    group: root
    mode: 0750
  when:
    - certbot_create_standalone_stop_services is defined
    - certbot_create_standalone_stop_services

- name: Create post hook to start services.
  template:
    src: start_services.j2
    dest: /etc/letsencrypt/renewal-hooks/post/start_services
    owner: root
    group: root
    mode: 0750
  when:
    - certbot_create_standalone_stop_services is defined
    - certbot_create_standalone_stop_services

- name: Generate new certificate if one doesn't exist.
  command: "{{ certbot_create_command }}"
  when: not letsencrypt_cert.stat.exists
