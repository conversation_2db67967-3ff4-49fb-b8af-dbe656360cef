---
- name: Check if certificate already exists.
  stat:
    path: /etc/letsencrypt/live/{{ cert_item.domains | first }}/cert.pem
  register: letsencrypt_cert

- name: Create webroot directory if it doesn't exist yet
  file:
    path: "{{ cert_item.webroot | default(certbot_webroot) }}"
    state: directory

- name: Generate new certificate if one doesn't exist.
  command: "{{ certbot_create_command }}"
  when: not letsencrypt_cert.stat.exists
