---
# To run:
#   1. Ensure Ansible and <PERSON><PERSON> are installed (pip install ansible boto).
#   2. Ensure you have AWS credentials stored where <PERSON><PERSON> can find them, and they
#      are under the profile 'mm'.
#   3. Ensure you have a pubkey available at ~/.ssh/id_rsa.pub.
#   3. Run the playbook: ansible-playbook test-standalone-nginx-aws.yml

# Play 1: Provision EC2 instance and A record.
- hosts: localhost
  connection: local
  gather_facts: false

  tasks:
    - name: Configure EC2 Security Group.
      ec2_group:
        profile: mm
        name: certbot_test_http
        description: HTTP security group for Certbot testing.
        region: "us-east-1"
        state: present
        rules:
          - proto: tcp
            from_port: 80
            to_port: 80
            cidr_ip: 0.0.0.0/0
          - proto: tcp
            from_port: 443
            to_port: 443
            cidr_ip: 0.0.0.0/0
          - proto: tcp
            from_port: 22
            to_port: 22
            cidr_ip: 0.0.0.0/0
        rules_egress: []

    - name: Add EC2 Key Pair.
      ec2_key:
        profile: mm
        region: "us-east-1"
        name: certbot_test
        key_material: "{{ item }}"
      with_file:
        - ~/.ssh/id_rsa.pub

    - name: Provision EC2 instance.
      ec2:
        profile: mm
        key_name: certbot_test
        instance_tags:
          Name: "certbot-standalone-nginx-test"
        group: ['default', 'certbot_test_http']
        instance_type: t2.micro
        # CentOS Linux 7 x86_64 HVM EBS
        image: ami-02e98f78
        region: "us-east-1"
        wait: true
        wait_timeout: 500
        exact_count: 1
        count_tag:
          Name: "certbot-standalone-nginx-test"
      register: created_instance

    - name: Add A record for the new EC2 instance IP in Route53.
      route53:
        profile: mm
        command: create
        zone: servercheck.in
        record: certbot-test.servercheck.in
        type: A
        ttl: 300
        value: "{{ created_instance.tagged_instances.0.public_ip }}"
        wait: true
        overwrite: true

    - name: Add EC2 instance to inventory groups.
      add_host:
        name: "certbot-test.servercheck.in"
        groups: "aws,aws_nginx"
        ansible_ssh_user: centos
        host_key_checking: false
      when: created_instance.tagged_instances.0.id is defined

# Play 2: Configure EC2 instance with Certbot and Nginx.
- hosts: aws_nginx
  gather_facts: true
  become: true

  vars:
    certbot_admin_email: <EMAIL>
    certbot_create_if_missing: true
    certbot_create_standalone_stop_services: []
    certbot_certs:
      - domains:
          - certbot-test.servercheck.in
    nginx_vhosts:
      - listen: "443 ssl http2"
        server_name: "certbot-test.servercheck.in"
        root: "/usr/share/nginx/html"
        index: "index.html index.htm"
        state: "present"
        template: "{{ nginx_vhost_template }}"
        filename: "certbot_test.conf"
        extra_parameters: |
          ssl_certificate     /etc/letsencrypt/live/certbot-test.servercheck.in/fullchain.pem;
          ssl_certificate_key /etc/letsencrypt/live/certbot-test.servercheck.in/privkey.pem;
          ssl_protocols       TLSv1.1 TLSv1.2;
          ssl_ciphers         HIGH:!aNULL:!MD5;

  pre_tasks:
    - name: Update apt cache.
      apt: update_cache=true cache_valid_time=600
      when: ansible_os_family == 'Debian'
      changed_when: false

    - name: Install dependencies (RedHat).
      yum: name={{ item }} state=present
      when: ansible_os_family == 'RedHat'
      with_items:
        - cronie
        - epel-release

    - name: Install cron (Debian).
      apt: name=cron state=present
      when: ansible_os_family == 'Debian'

  roles:
    - geerlingguy.certbot
    - geerlingguy.nginx

  tasks:
    - name: Flush handlers in case any configs have changed.
      meta: flush_handlers

    - name: Test secure connection to SSL domain.
      uri:
        url: https://certbot-test.servercheck.in/
        status_code: 200
      delegate_to: localhost
      become: false

# Play 3: Tear down EC2 instance and A record.
- hosts: localhost
  connection: local
  gather_facts: false

  tasks:
    - name: Destroy EC2 instance.
      ec2:
        profile: mm
        instance_ids: ["{{ created_instance.tagged_instances.0.id }}"]
        region: "us-east-1"
        state: absent
        wait: true
        wait_timeout: 500

    - name: Delete Security Group.
      ec2_group:
        profile: mm
        name: certbot_test_http
        region: "us-east-1"
        state: absent

    - name: Delete Key Pair.
      ec2_key:
        profile: mm
        name: certbot_test
        region: "us-east-1"
        state: absent

    - name: Delete Route53 record.
      route53:
        profile: mm
        state: delete
        zone: servercheck.in
        record: certbot-test.servercheck.in
        type: A
        ttl: 300
        # See: https://github.com/ansible/ansible/pull/32297
        value: []
