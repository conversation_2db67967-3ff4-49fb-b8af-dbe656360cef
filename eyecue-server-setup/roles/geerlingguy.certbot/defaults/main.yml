---
# Certbot auto-renew cron job configuration (for certificate renewals).
certbot_auto_renew: true
certbot_auto_renew_user: "{{ ansible_user | default(lookup('env', 'USER')) }}"
certbot_auto_renew_hour: "3"
certbot_auto_renew_minute: "30"
certbot_auto_renew_options: "--quiet --no-self-upgrade"

certbot_testmode: false
certbot_hsts: false


# Parameters used when creating new Certbot certs.
certbot_create_if_missing: false
certbot_create_method: standalone
certbot_admin_email: <EMAIL>

# Default webroot, overwritten by individual per-cert webroot directories
certbot_webroot: /var/www/letsencrypt

certbot_certs: []
# - email: <EMAIL>
#   webroot: "/var/www/html/"
#   domains:
#     - example1.com
#     - example2.com
# - domains:
#     - example3.com

certbot_create_command: >-
  {{ certbot_script }} certonly --{{ certbot_create_method  }}
  {{ '--hsts' if certbot_hsts else '' }}
  {{ '--test-cert' if certbot_testmode else '' }}
  --noninteractive --agree-tos
  --email {{ cert_item.email | default(certbot_admin_email) }}
  {{ '--webroot-path ' if certbot_create_method == 'webroot'  else '' }}
  {{ cert_item.webroot | default(certbot_webroot) if certbot_create_method == 'webroot' else '' }}
  -d {{ cert_item.domains | join(',') }}
  {{ '--pre-hook /etc/letsencrypt/renewal-hooks/pre/stop_services'
    if certbot_create_standalone_stop_services
  else '' }}
  {{ '--post-hook /etc/letsencrypt/renewal-hooks/post/start_services'
    if certbot_create_standalone_stop_services
  else '' }}

certbot_create_standalone_stop_services:
  - nginx
  # - apache
  # - varnish

# Available options: 'package', 'snap', 'source'.
certbot_install_method: 'package'

# Source install configuration.
certbot_repo: https://github.com/certbot/certbot.git
certbot_version: master
certbot_keep_updated: true

# Where to put Certbot when installing from source.
certbot_dir: /opt/certbot
