---
- name: Get all ArgoCD Resources
  kubernetes.core.k8s_info:
    kubeconfig: "{{ argocd_kubeconfig_location }}"
    namespace: "{{ argocd_namespace }}"
    api_version: argoproj.io/v1alpha1
    kind: "{{ item }}"
  loop:
    - AppProject
    - Application
    - ApplicationSet
  register: argocd_resources

- name: Remove finalizers on the eyecue ArgoCD resources
  kubernetes.core.k8s:
    state: patched
    kubeconfig: "{{ argocd_kubeconfig_location }}"
    wait: true
    namespace: "{{ argocd_namespace }}"
    api_version: argoproj.io/v1alpha1
    kind: "{{ item.kind }}"
    name: "{{ item.metadata.name }}"
    definition:
      metadata:
        finalizers: []
  loop: "{{ argocd_resources.results | json_query('[*].resources[*]') | flatten }}"

- name: Delete the argocd namespace
  kubernetes.core.k8s:
    state: absent
    kubeconfig: "{{ argocd_kubeconfig_location }}"
    wait: true
    force: true
    definition:
      apiVersion: v1
      kind: Namespace
      metadata:
        name: "{{ argocd_namespace }}"

- name: Delete everything else ArgoCD related
  kubernetes.core.k8s:
    state: absent
    kubeconfig: "{{ argocd_kubeconfig_location }}"
    wait: true
    kind: "{{ item }}"
    label_selectors:
      - app.kubernetes.io/part-of=argocd
  loop:
    - ClusterRole
    - ClusterRoleBinding
    - CustomResourceDefinition
