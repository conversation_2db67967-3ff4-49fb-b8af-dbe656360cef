---
# As we can't use an updated version of the helm chart but require
# the v2.6.0 CRDs we install them outside of the helm chart here
- name: Install ArgoCD CRDs
  kubernetes.core.k8s:
    state: present
    kubeconfig: "{{ argocd_kubeconfig_location }}"
    namespace: "{{ argocd_namespace }}"
    definition: "{{ item }}"
  no_log: true
  loop:
    - '{{ lookup("url", "https://raw.githubusercontent.com/argoproj/argo-cd/refs/tags/v2.6.0/manifests/crds/application-crd.yaml", split_lines=False) }}'
    - '{{ lookup("url", "https://raw.githubusercontent.com/argoproj/argo-cd/refs/tags/v2.6.0/manifests/crds/appproject-crd.yaml", split_lines=False) }}'
    - '{{ lookup("url", "https://raw.githubusercontent.com/argoproj/argo-cd/refs/tags/v2.6.0/manifests/crds/applicationset-crd.yaml", split_lines=False) }}'

- name: Deploy ArgoCD chart
  kubernetes.core.helm:
    release_name: argocd
    chart_ref: argo-cd
    chart_repo_url: https://argoproj.github.io/argo-helm
    chart_version: "{{ argocd_helm_version }}"
    kubeconfig: "{{ argocd_kubeconfig_location }}"
    release_namespace: "{{ argocd_namespace }}"
    create_namespace: true
    wait: true
    # The default values below can be found here (as of helm chart version 5.7.0, which is the argocd version 2.4.15):
    # https://github.com/argoproj/argo-helm/blob/argo-cd-5.7.0/charts/argo-cd/values.yaml
    values:
      global:
        image:
          # override the default image a more recent version which includes
          # support for multiple sources in the same application
          tag: v2.6.0

      crds:
        install: false

      dex:
        # we are not using SSO, so we disable it
        enabled: false

      controller:
        metrics:
          enabled: true

          service:
            annotations:
              prometheus.io/scrape: "true"
              prometheus.io/path: "/metrics"
              prometheus.io/port: "8082"

      server:
        metrics:
          enabled: true

          service:
            annotations:
              prometheus.io/scrape: "true"
              prometheus.io/path: "/metrics"
              prometheus.io/port: "8083"

        service:
          type: NodePort

      repoServer:
        metrics:
          enabled: true

          service:
            annotations:
              prometheus.io/scrape: "true"
              prometheus.io/path: "/metrics"
              prometheus.io/port: "8084"

        volumes:
          # we use this to install an updated helm version which is required by some of our dependencies.
          # And we are on an old kubernetes version so we can't update to the latest argocd helm chart
          - name: helm-bin
            emptyDir: {}

        volumeMounts:
          - name: helm-bin
            mountPath: /usr/local/bin/helm
            subPath: helm

        env:
          - name: AWS_ACCESS_KEY_ID
            valueFrom:
              secretKeyRef:
                name: argocd-secret
                key: AWS_ACCESS_KEY_ID
          - name: AWS_SECRET_ACCESS_KEY
            valueFrom:
              secretKeyRef:
                name: argocd-secret
                key: AWS_SECRET_ACCESS_KEY
          - name: AWS_REGION
            valueFrom:
              secretKeyRef:
                name: argocd-secret
                key: AWS_REGION

        # https://argo-cd.readthedocs.io/en/stable/user-guide/helm/#helm-plugins
        # https://medium.com/@venkatarajv/supporting-argocd-for-s3-helm-charts-with-init-container-support-35567a3e1bcd
        initContainers:
          - name: helm-s3-plugin
            image: hypnoglow/helm-s3:{{ argocd_helm_s3_plugin_version }}-helm3.17
            volumeMounts:
              - name: helm-working-dir
                mountPath: /helm-working-dir
              - name: helm-bin
                mountPath: /tmp/helm-bin
            env:
              - name: HELM_DATA_HOME
                value: /helm-working-dir
              - name: HELM_CACHE_HOME
                value: /helm-working-dir
              - name: HELM_CONFIG_HOME
                value: /helm-working-dir
              # we add the AWS credentials to the container from
              # the secret at configs.secret.extra below
              - name: AWS_ACCESS_KEY_ID
                valueFrom:
                  secretKeyRef:
                    name: argocd-secret
                    key: AWS_ACCESS_KEY_ID
              - name: AWS_SECRET_ACCESS_KEY
                valueFrom:
                  secretKeyRef:
                    name: argocd-secret
                    key: AWS_SECRET_ACCESS_KEY
              - name: AWS_REGION
                valueFrom:
                  secretKeyRef:
                    name: argocd-secret
                    key: AWS_REGION
            command: [ "/bin/sh", "-c" ]
            # This is a hacky way to install the helm-s3 plugin which varies from the documentation.
            # This is because we cannot use the `helm plugin install` command on some servers due to
            # firewall restrictions so instead we copy the binary from the helm-s3 image directly.
            args:
              - |
                set -e
                # here is where we get the updated helm binary
                cp $(which helm) /tmp/helm-bin/helm

                mkdir -p /helm-working-dir/plugins/helm-s3.git
                cp -r /root/.helm/cache/plugins/helm-s3/* /helm-working-dir/plugins/helm-s3.git

                chmod -R 777 $HELM_DATA_HOME

      configs:
        params:
          server.insecure: true

        secret:
          argocdServerAdminPassword: "{{ argocd_admin_password_hashed }}"
          extra:
            AWS_ACCESS_KEY_ID: "{{ argocd_helm_aws_access_key }}"
            AWS_SECRET_ACCESS_KEY: "{{ argocd_helm_aws_secret_key }}"
            AWS_REGION: "{{ argocd_helm_aws_region }}"

        repositories:
          eyecue-helm-repo:
            url: "{{ argocd_bitbucket_repo }}"
            insecureIgnoreHostKey: "true"

          infra-helm-repo:
            url: *****************:fingermarkltd/eyecue-infra-helm-charts.git
            insecureIgnoreHostKey: "true"

        credentialTemplates:
          eyecue-helm-credentials:
            url: "{{ argocd_bitbucket_repo }}"
            sshPrivateKey: "{{ argocd_eyecue_ssh_private_key }}"

          infra-helm-credentials:
            url: *****************:fingermarkltd/eyecue-infra-helm-charts.git
            sshPrivateKey: "{{ argocd_eyecue_ssh_private_key }}"

      extraObjects:
        - apiVersion: v1
          kind: Service
          metadata:
            labels: &proxy-labels
              app.kubernetes.io/component: s3-proxy
              app.kubernetes.io/name: argocd-s3-proxy
              app.kubernetes.io/instance: argocd
              app.kubernetes.io/part-of: argocd
            name: argocd-s3-proxy
          spec:
            ports:
            - name: server
              port: 80
              protocol: TCP
              targetPort: 80
            selector: *proxy-labels
        - apiVersion: apps/v1
          kind: Deployment
          metadata:
            labels: *proxy-labels
            name: argocd-s3-proxy
          spec:
            selector:
              matchLabels: *proxy-labels
            template:
              metadata:
                labels: *proxy-labels
              spec:
                containers:
                  - name: s3-proxy
                    image: pottava/s3-proxy:2.0
                    env:
                      - name: AWS_ACCESS_KEY_ID
                        valueFrom:
                          secretKeyRef:
                            name: argocd-secret
                            key: AWS_ACCESS_KEY_ID
                      - name: AWS_SECRET_ACCESS_KEY
                        valueFrom:
                          secretKeyRef:
                            name: argocd-secret
                            key: AWS_SECRET_ACCESS_KEY
                      - name: AWS_REGION
                        valueFrom:
                          secretKeyRef:
                            name: argocd-secret
                            key: AWS_REGION
                      - name: AWS_S3_BUCKET
                        value: "{{ argocd_helm_bucket_name }}"
                    ports:
                      - containerPort: 80
                    resources:
                      requests:
                        memory: 50Mi
                        cpu: 10m
                      limits:
                        memory: 100Mi
                        cpu: 50m

- name: Deploy ArgoCD Apps chart
  kubernetes.core.helm:
    release_name: argocd-apps
    chart_ref: argocd-apps
    chart_repo_url: https://argoproj.github.io/argo-helm
    chart_version: "{{ argocd_helm_apps_version }}"
    kubeconfig: "{{ argocd_kubeconfig_location }}"
    release_namespace: "{{ argocd_namespace }}"
    create_namespace: true
    wait: true
    # The default values below can be found here (as of helm chart version 1.6.2)
    # https://github.com/argoproj/argo-helm/blob/argocd-apps-1.6.2/charts/argocd-apps/values.yaml
    # We are using argocd version 2.6, the documentation for the declarative setup can be found here:
    # https://argo-cd.readthedocs.io/en/release-2.6/operator-manual/declarative-setup/#applications
    values:
      applications:
        - name: argocd-apps
          namespace: "{{ argocd_namespace }}"
          project: argocd-apps
          finalizers:
            - resources-finalizer.argocd.argoproj.io
          source:
            repoURL: *****************:fingermarkltd/eyecue-infra-helm-charts.git
            targetRevision: HEAD
            path: charts/argocd-apps
            helm:
              version: v3
              releaseName: argocd-apps
              ignoreMissingValueFiles: true
              valueFiles:
                - ../../values-overrides/argocd-apps/organization/{{ argocd_organization }}.yaml
                - ../../values-overrides/argocd-apps/site/{{ ansible_hostname }}.yaml
              values: |
                siteId: "{{ ansible_hostname }}"
                organization: "{{ argocd_organization }}"
                timezone: "{{ timezone }}"
                applications:
                  eyecue:
                    source:
                      repoURL: "{{ argocd_bitbucket_repo }}"
                projects:
                  eyecue:
                    sourceRepos:
                      - "{{ argocd_bitbucket_repo }}"
          destination:
            server: https://kubernetes.default.svc
            namespace: "{{ argocd_namespace }}"
          syncPolicy:
            automated:
              prune: true
              selfHeal: true
              allowEmpty: true
            syncOptions:
              - CreateNamespace=true
              - PruneLast=true
            retry:
              limit: 5

      projects:
        - name: argocd-apps
          namespace: "{{ argocd_namespace }}"
          permitOnlyProjectScopedClusters: false
          finalizers:
            - resources-finalizer.argocd.argoproj.io
          description: "This project is for the argocd-apps chart"
          sourceRepos:
            - *****************:fingermarkltd/eyecue-infra-helm-charts.git
          destinations:
            - namespace: "{{ argocd_namespace }}"
              server: https://kubernetes.default.svc
          clusterResourceWhitelist:
            - group: '*'
              kind: '*'
