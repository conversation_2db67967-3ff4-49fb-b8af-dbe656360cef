- name: Send notification message to Slack channel
  slack:
    username: "{{ slack_username }}"
    icon_url: "{{ slack_icon_url }}"
    token: "{{ slack_token }}"
    channel: "{{ slack_channel }}"

    blocks:
      - type: section
        text:
          type: mrkdwn
          text: |-
            *OS Update Notification*

      - type: divider

      - type: section
        text:
          type: mrkdwn
          text: |-
            `{{ ansible_hostname | default('unknown') }}` is receiving an Ubuntu update - from 18.04 to 22.04 :happy-cj:

      - type: divider

      - type: section
        text:
          type: mrkdwn
          text: |-
            *Site Name*: `{{ display_name | default('unknown') }}`
            *Hostname*: `{{ ansible_hostname | default('unknown') }}`
            *Timezone*: `{{ ansible_date_time.tz | default('unknown') }}`

      - type: divider
