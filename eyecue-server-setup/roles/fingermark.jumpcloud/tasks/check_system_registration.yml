---
- name: Retrieve System attributes based on the System ID
  uri:
    url: "{{ jumpcloud_api_v1_url }}/systems/{{ jumpcloud_system_key }}"
    method: GET
    headers:
      "Content-Type": "application/json"
      "Accept": "application/json"
      "x-api-key": "{{ jumpcloud_api_key }}"
    follow_redirects: all
    return_content: true
    status_code: 200

  register: jc_system_attributes_json_response
  ignore_errors: true
  when: jumpcloud_system_key is defined

- name: Define if System is registered in JumpCloud
  set_fact:
    jc_system_is_registered: "{{ not jc_system_attributes_json_response.failed }}"
  when: jc_system_attributes_json_response is defined

- debug:
    msg: "The System is registered in JumpCloud"
  when: jc_system_is_registered

- debug:
    msg: "Warning ==> the System is not registered in JumpCloud"
  when: not jc_system_is_registered
