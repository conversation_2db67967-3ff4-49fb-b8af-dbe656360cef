---
- name: Download JumpCloud Kickstart script
  get_url:
    url: "{{ jumpcloud_x_connect_url }}"
    dest: "{{ jumpcloud_kickstart_script }}"
    mode: 0755
    headers:
      x-connect-key: "{{ jumpcloud_x_connect_key }}"
  become: "{{ jumpcloud_use_sudo }}"

- name: Install JumpCloud Agent
  command: "{{ jumpcloud_kickstart_script }}"
  args:
    creates: "{{ jumpcloud_agent_config }}"
  become: true

- name: Check JumpCloud agent config again
  stat:
    path: "{{ jumpcloud_agent_config }}"
  register: jumpcloud_agent_config_status
  become: "{{ jumpcloud_use_sudo }}"

- name: Check again if JumpCloud has been initialised
  set_fact:
    jumpcloud_is_installed: "{{ jumpcloud_agent_config_status.stat.isreg is defined and jumpcloud_agent_config_status.stat.isreg }}"

- name: Reset JumpCloud if `jcagent.conf` has not been created
  import_tasks: reset_jumpcloud.yml
  when: not jumpcloud_is_installed

- name: Restore '/etc/issue' backup if present
  file:
    src: "{{ issue_backup_file }}"
    path: /etc/issue
    state: hard
    force: yes
  when: issue_backup_file is defined

- name: Removes '/etc/issue' backup file if present
  file:
    path: "{{ issue_backup_file }}"
    state: absent
  when: issue_backup_file is defined
