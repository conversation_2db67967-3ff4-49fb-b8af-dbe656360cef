---
jumpcloud_api_key: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  37356365373434663934366631316339633031656436656165343764376431666135653236363737
  3734366265356461323137353566353464623036363139340a343466626636383134353864333630
  63396436646165613533353136613138373035343737663963663164623733663162363265383832
  3233663835393362340a393134666630346265643665336163623566346665353932303262663732
  63383231356461636162373634623965366334663431663235623633363035653239393232373636
  3665386235356236336665313836663530663039313136396237
jumpcloud_x_connect_key: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  30356437306330613761386234336130386137373936393331356362636466633963356236323034
  3366663266386664373465373530636133613961663237340a346335333934366265346336343263
  31396637613034323431303065373437636537343463343237356563306634616430303833373464
  6531643133626633630a366366633661636234666466353539636264626536376134336631656637
  34643765636130386335353636353535656466653130323862363632343965306333616261366132
  3633386230333738343131313861613764343534356464393639
jumpcloud_directory: /opt/jc
jumpcloud_ca_cert: "{{ jumpcloud_directory }}/ca.crt"
jumpcloud_client_cert: "{{ jumpcloud_directory }}/client.crt"
jumpcloud_client_key: "{{ jumpcloud_directory }}/client.key"
jumpcloud_agent_config: "{{ jumpcloud_directory }}/jcagent.conf"
jumpcloud_x_connect_url: "https://kickstart.jumpcloud.com/Kickstart"
jumpcloud_kickstart_script: "/tmp/Kickstart.sh"
jumpcloud_api_v1_url: "https://console.jumpcloud.com/api"
jumpcloud_api_v2_url: "https://console.jumpcloud.com/api/v2"
jumpcloud_agent_service: jcagent
jumpcloud_force_install: false
jumpcloud_use_sudo: true
jumpcloud_displayName: "{{ inventory_hostname }}"
jc_url: "https://console.jumpcloud.com/api/v2/systemgroups/{{ jc_device_id }}/members"
jumpcloud_dependencies: []
jumpcloud_all_dependencies:
  - openssl
  - coreutils
  - curl
  - grep
  - lsof
  - tar
  - sudo
jumpcloud_yum_dependencies:
  - chkconfig
  - chkconfig
  - findutils
  - gawk
  - glibc-common
  - initscripts
  - net-tools
  - rpm
  - shadow-utils
  - util-linux
  - which
  - yum
  - redhat-lsb-core
  - psmisc
jumpcloud_apt_dependencies:
  - apt-show-versions
  - apt-rdepends
  - dpkg
  - hostname
  - libc-bin
  - lsb-release
  - mawk
  - passwd
  - procps
  - sysvinit-utils
jumpcloud_debian_dependencies:
  - sysv-rc
  - libpam-runtime
  - libpam-modules
  - psmisc
jumpcloud_ubuntu_dependencies: []
