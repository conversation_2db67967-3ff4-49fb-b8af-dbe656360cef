network:
  version: 2
  renderer: networkd
  ethernets:
    vpn_{{ softether_client_vpn_nic_name }}:
     dhcp4: no
     addresses: 
     - "{{ softether_client_private_ip }}/24"
     routes:
       - to: 10.0.0.0/8
         via: *************
         metric: 10
       - to: ************/24
         via: *************
         metric: 10
     nameservers:
       addresses: [**************] 
       search: [eyeq.vpn]
