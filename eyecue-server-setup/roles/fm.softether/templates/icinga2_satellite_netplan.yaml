network:
  version: 2
  renderer: networkd
  ethernets:
    vpn_{{ softether_client_vpn_nic_name }}:
      dhcp4: no
      addresses: 
      - {{ softether_client_private_ip }}/22
      routes:
      - to: ************/24
        via: 10.{{ softether_client_private_ip[3:5] }}.0.2
        metric: 10
      - to: *************/24
        via: 10.{{ softether_client_private_ip[3:5] }}.0.2
        metric: 10
      nameservers:
        addresses: [**************] 
        search: [eyeq.vpn]
