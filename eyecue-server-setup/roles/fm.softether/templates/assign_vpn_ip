#!/bin/bash

export PATH="/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games"

HASIP=$(ip address show vpn_{{ vpn_virtual_hub }} | grep {{ vpn_client_ip }} | awk '{print $1}' )

if [ -z $HASIP ];
then
    ifconfig vpn_{{ vpn_virtual_hub }} {{ vpn_client_ip }}/{{ vpn_client_net_mask }}
    route add -net *************/24 gw {{ vpn_client_gw }}
    route add -net ************/24 gw {{ vpn_client_gw }}
fi
