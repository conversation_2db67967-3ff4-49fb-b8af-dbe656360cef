fm.softether
=========

Role for installing and setting up Softether VPN Client inside Fingermark Servers and regular clients

Requirements
------------

make and gcc packages must be installed

Role Variables
--------------

Before using this role, you have to properly set the variables according to the vpnclient you are installing.

If the role is being deployed in an EyeCue Server you just need to write the VPN password on the Playbook. If you are installing Softether on your computer, please also enter your softether_client_username and the hubname (fm-team)



Example Playbook
----------------

### Eyecue Sever

    


Including an example of how to use your role (for instance, with variables passed in as parameters) is always nice for users too:

        ---
        - name: Install Softether VPN client
        hosts: carroltoneast
        become: yes
        vars:
        - ansible_user: fingermark
        - vpn_user_password: 
        roles:
        - fm.softether

### Your Computer

If you are installing on your computer, use as the example:

        ---
        - name: Install Softether VPN client
        hosts: localhost
        connection: local
        become: yes
        vars:
        - softether_virtualhub:  fm-team
        - softether_client_username: <your_username>
        - vpn_user_password: <your_password>
        roles:
        - fm.softether

Complete with you user and password

Run Playbook
------------

### On an EyeCue server

``` ansible-playbook -i <inventory> playbooks/softether.yml --ask-vault-pass```

### On your computer

``` ansible-playbook playbooks/softether.yml --ask-vault-pass```

License
-------

BSD

