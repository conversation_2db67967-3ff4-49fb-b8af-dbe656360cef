- name: Download Softether Client
  get_url: 
    url: "{{ softether_download_link }}"
    dest: "{{ softether_dest_dir }}"
  register: softether_package

- name: Decompress Softether Package
  unarchive:
      src: "{{softether_package.dest}}"
      dest: "{{ softether_dest_dir }}"
      remote_src: yes

- name: Start Installation
  make:
    chdir: "{{ softether_dest_dir }}/vpnclient"
    #target: i_read_and_agree_the_license_agreement
      
- name: Create Startup Script
  copy:
      src: vpnclient
      dest: /etc/init.d/vpnclient
      mode: +x

- name: Create Symbolic Links 
  file: 
    src: '/etc/init.d/vpnclient'
    dest: '{{ item.dest }}'
    state: link
  loop:
    - { dest: '/usr/local/bin/vpnclient' }
    - { dest: '/etc/rc3.d/S01vpnclient' }
  
- name: Delete Softether package
  file: 
    state: absent
    path: "{{ softether_package.dest }}"

