- name: Configure netplan for VPN with DHCP
  template:
    src: netplan_config_dhcp.yaml
    dest: /etc/netplan/02-softether.yaml
  when: softether_virtualhub != "servers" and not icinga2_satellite_setup  

- name: Configure netplan for VPN with Static IP
  template:
    src: netplan_config_nodhcp.yaml
    dest: /etc/netplan/02-softether.yaml
  when: softether_virtualhub == "servers" or softether_virtualhub == "SERVERS"

- name: "Copying Netplan's config for Icinga2 Satellite Instance"
  template: 
    src: icinga2_satellite_netplan.yaml
    dest: /etc/netplan/02-softether.yaml
  when: icinga2_satellite_setup  

- shell: "vpnclient stop && vpnclient start"

- name: Apply network configuration
  command: netplan apply
