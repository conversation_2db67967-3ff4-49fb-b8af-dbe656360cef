---
- name: Ensuring the services is disabled
  ansible.builtin.systemd: 
      name: "{{ item.name }}"
      enabled: "{{ item.enabled }}"
      masked: "{{ item.masked }}"
  loop: 
    - { name: suspend.target, enabled: false, masked: true }
    - { name: sleep.target, enabled: false, masked: true }
    - { name: hibernate.target, enabled: false, masked: true }
    - { name: hybrid-sleep.target, enabled: false, masked: true }


- name: Delete GUI packages
  ansible.builtin.apt:
    name: 
      - tasksel
      - ubuntu-desktop
    state: absent
    purge: true
    autoremove: true

# - name: Get systemctl boot default
#   ansible.builtin.command: systemctl get-default
#   changed_when: false
#   register: systemctl_get_default_output

# - name: Set systemctl boot to multi-user
#   ansible.builtin.command: systemctl set-default multi-user
#   when: systemctl_get_default_output.stdout == "graphical.target"