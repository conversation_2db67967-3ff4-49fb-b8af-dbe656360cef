
- name: "Stopping teleport service"
  ansible.builtin.systemd: 
    name: teleport
    state: stopped
  ignore_errors: yes

- name: "Removing Teleport cluster"
  ansible.builtin.apt:
    name: teleport
    state: absent
  ignore_errors: yes

- name: "Removing Teleport cluster"
  ansible.builtin.file:
    path: "/var/lib/teleport"
    state: absent
  ignore_errors: yes

- name: "Removing Teleport cluster"
  ansible.builtin.file:
    path: "/etc/teleport.yaml"
    state: absent
  ignore_errors: yes
