---

- name: Check if Teleport is already installed
  stat: 
    path: /usr/local/bin/teleport
  register: teleport_cli

- name: Check current Teleport version
  shell: teleport version 2>/dev/null | grep ^Teleport | awk '{print $2}' || echo "none"
  register: current_teleport_version
  changed_when: false

- name: Delete existing cluster if version mismatch
  include_tasks: delete_cluster.yml
  when: current_teleport_version.stdout != teleport_version and current_teleport_version.stdout != "none"

- name: Install Teleport
  include_tasks: install.yml
  when: current_teleport_version.stdout != teleport_version

- name: Setup Teleport
  include_tasks: setup.yml

- name: Validate Teleport service status
  systemd:
    name: teleport
    state: started
  register: teleport_service_status
