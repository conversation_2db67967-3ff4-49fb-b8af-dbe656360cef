---

- name: Generate node token
  ansible.builtin.set_fact:
    node_token: "{{ lookup('password', '/dev/null length=30 chars=ascii_lowercase') }}"

- name: Setup Teleport node
  block:
    - name: Generate token on Teleport server
      shell: tctl tokens add --ttl=1h --type=node --value {{ node_token }}
      delegate_to: "{{ teleport_server }}"
      register: teleport_node_token

    - name: Get CA pin from Teleport server
      shell: tctl status | grep "CA pin" | awk '{print $3}'
      delegate_to: "{{ teleport_server }}"
      register: teleport_ca_pin

    - name: Deploy Teleport configuration
      template:
        src: teleport.yaml
        dest: /etc/teleport.yaml
        mode: '0644'

    - name: Enable and start Teleport service
      systemd:
        name: teleport
        enabled: yes
        state: restarted
        daemon_reload: yes
  rescue:
    - name: Handle setup failure
      fail:
        msg: "Failed to setup Teleport node. Please check connectivity to Teleport server and try again."
