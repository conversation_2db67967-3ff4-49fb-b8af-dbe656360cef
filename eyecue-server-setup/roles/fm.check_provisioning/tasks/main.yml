- name: "ansible_os_family"
  ansible.builtin.debug:
    msg: "{{ ansible_os_family }}"

- ansible.builtin.set_fact:
    block_file_path: "/opt/.SLACK_NOTIFICATION_SENT"

- ansible.builtin.include_tasks:
    file: checks.yml

- name: "Checking if block file is in place"
  ansible.builtin.stat:
    path: "{{ block_file_path }}"
  register: block_file

- ansible.builtin.debug:
    msg: "{{block_file.stat}}"

- name: Sending Slack notification
  ansible.builtin.include_tasks:
    file: slack_notification.yml
    apply:
      tags:
        - notification
  when: send_slack_notification and not block_file.stat.exists

- name: Creating file to block next notifications 
  ansible.builtin.file:
    path: "{{ block_file_path }}"
    state: touch
  when: not block_file.stat.exists
