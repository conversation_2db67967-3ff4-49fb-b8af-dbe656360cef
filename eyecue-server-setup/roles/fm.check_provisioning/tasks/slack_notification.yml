- name: Send notification message to Slack channel
  slack:
    username: "{{ slack_username }}"
    icon_url: "{{ slack_icon_url }}"
    token: "{{ slack_token }}"
    channel: "{{ slack_channel }}"
    blocks:
      - type: header
        text:
          type: plain_text
          text: |-
            {{ display_name }} - Provision Result
      - type: section
        text:
          type: mrkdwn
          text: |-
            *Serial Number*: `{{ serial_number_result.stdout | default('unknown') }}`
            *Hostname*: `{{ ansible_hostname | default('unknown') }}`
            *UUID*: `{{ uuid_result | replace("['","") | replace("']", "") }}`
            *Timezone*: `{{ ansible_date_time.tz | default('unknown') }}`
      - type: section
        fields:

          - type: mrkdwn
            text: |-
              *Secure boot status* {{ slack_optional_hint }} {%- if not secure_boot_result.failed and "SecureBoot disabled" in secure_boot_result.stdout %} {{slack_ok_icon}} {%- else %} {{slack_error_icon}} {%- endif %}

              ```
                {%- if secure_boot_result.stderr %} {{ secure_boot_result.stderr }} {%- else %} {{ secure_boot_result.stdout | default('') }} {%- endif %}
              ```

          - type: mrkdwn
            text: |-
              *Teleport Status* {{ slack_optional_hint }} {%- if not teleport_output.failed %} {{slack_ok_icon}} {%- else %} {{slack_error_icon}} {%- endif %}
            
              ```
              Teleport is {{ teleport_output.status.ActiveState | default('') }} since {{ teleport_output.status.ActiveEnterTimestamp | default('') }}
              ```

          - type: mrkdwn
            text: |-
              *VPN Status* {{ slack_optional_hint }} {%- if not ping_result.failed %} {{slack_ok_icon}} {%- else %} {{slack_error_icon}} {%- endif %}

              ```
              {%- if not ping_result.failed %} The VPN is working well {%- else %} The VPN is down {%- endif %}
              ```
          - type: mrkdwn
            text: |-
              *Nvidia Driver* {{ slack_optional_hint }} {%- if not nvidia_smi_output.failed %} {{slack_ok_icon}}
              ```
              {{ nvidia_smi_output.stdout_lines[2] | replace('|','') | trim }}
              ```
              {%- else %} {{slack_error_icon}}
              ```
              stdout={{ nvidia_smi_output.stdout | default('') }}
              stderr={{ nvidia_smi_output.stderr | default('') }}
              ```
              {%- endif %}
          - type: mrkdwn
            text: |-
              *Kubernetes Cluster* {{ slack_optional_hint }} {%- if not kubelet_output.failed %} {{slack_ok_icon}}
              ```
              The k8s cluster is running on version {{ k8s_api_status.version.server.kubernetes.gitVersion }}
              ```
              {%- else %} {{slack_error_icon}}
              ```
              The kubelet service is not running
              ```
              {%- endif %}
          - type: mrkdwn
            text: |-
              *Icinga2 Status* {{ slack_optional_hint }} {%- if not icinga2_output.failed %} {{slack_ok_icon}} {%- else %} {{slack_error_icon}} {%- endif %}
            
              ```
              Icinga2 is {{ icinga2_output.status.ActiveState | default('') }} since {{ icinga2_output.status.ActiveEnterTimestamp | default('') }}
              ```
          - type: mrkdwn
            text: |-
              *Network Status* {{ slack_optional_hint }}{{slack_ok_icon}}
              ```
              NIC Name: {{ ansible_default_ipv4.alias | default('unknown') }}
              IP Address: {{ ansible_default_ipv4.address | default('unknown') }}/{{ ansible_default_ipv4.netmask | default('unknown') }}
              Gateway: {{ ansible_default_ipv4.gateway | default('unknown') }}
              ```
          - type: mrkdwn
            text: |-
              *Storage Status* {{ slack_optional_hint }} {%- if storage_mounted %} {{ slack_ok_icon }} {%- else %} {{ slack_error_icon }} {%- endif %}
              ```
              {%- if storage_mounted %}
                /media/fingermark/storage is mounted on {{ findmnt_result.stdout }}
              {%- else %}
                /media/fingermark/storage is not mounted
              {%- endif %}
              ```
      - type: divider
