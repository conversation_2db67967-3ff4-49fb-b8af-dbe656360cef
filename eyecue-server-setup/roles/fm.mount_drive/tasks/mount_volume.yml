---

- name: Check if selected device already has a filesystem
  command: blkid {{ selected_device }}
  register: fs_check
  changed_when: false
  failed_when: false

- name: Create ext4 filesystem on selected device with lazy initialization
  command: "mkfs.ext4 -E lazy_itable_init=1,lazy_journal_init=1 {{ selected_device }}"
  when: fs_check.rc != 0

- name: Check if device label "{{ storage_device_label }}" already exists
  stat:
    path: "/dev/disk/by-label/{{ storage_device_label }}"
  register: storage_label

- name: Label selected device with "{{ storage_device_label }}"
  command: e2label {{ selected_device }} {{ storage_device_label }}
  when: not storage_label.stat.exists

- name: Ensure mount directory "{{ storage_mount_path }}" exists
  file:
    path: "{{ storage_mount_path }}"
    state: directory
    owner: root
    group: root
    mode: '0755'

- name: Retrieve UUID for the selected device
  command: "find /dev/disk/by-uuid -lname '{{ selected_uuid_path }}' -printf %f"
  register: fs_uuid
  changed_when: false

- name: Mount storage device using UUID
  mount:
    path: "{{ storage_mount_path }}"
    src: "UUID={{ fs_uuid.stdout }}"
    fstype: auto
    opts: "nosuid,nodev,nofail,x-gvfs-show"
    state: mounted
