---
- name: set-registry
  set_fact:
    registry: "613615422941.dkr.ecr.{{region}}.amazonaws.com"

- name: Copy the script file
  copy:
    src: "images-{{region}}.yaml"
    dest: /root/images-to-pull.yaml

- name: Copy the script file
  copy:
    src: script.sh
    dest: /root/pull-images.sh

- name: "Logging into ECR"
  shell: "aws ecr get-login-password --region {{region}} | sudo docker login --username AWS --password-stdin {{ registry }}"
  environment:
    AWS_ACCESS_KEY_ID: "{{ access_key_id }}"
    AWS_SECRET_ACCESS_KEY: "{{ secret_access_key }}"
    AWS_DEFAULT_REGION: "{{ region }}"
  args:
    executable: "/bin/bash"

- name: Install screen package (if not already installed)
  package:
    name: screen
    state: present

- name: Execute shell command to delete screen sessions
  shell: |
    screen -ls | grep "download" | awk -F. '{print $1}'  | xargs -I{} screen -S {} -X quit

- name: Run the script in the screen session
  command: screen -dmS download bash -c "bash /root/pull-images.sh"
