ddns-update-style 		interim;
ddns-updates 			on;
ignore 				client-updates;
update-optimization		false;
ddns-rev-domainname		"in-addr.arpa.";
authoritative;
option classless-routes code 121 = array of unsigned integer 8;

ddns-domainname 		"eyeq.vpn";
zone eyeq.vpn {
  primary **************;
}

############################# MCD ###########################################

zone 20.10.in.addr.arpa. {
  primary **************;
}

subnet ********* netmask ************* {

  max-lease-time 86400;
  range ********* ***********;
  
  option subnet-mask *************;
  option dhcp-server-identifier *********;
  option broadcast-address ***********;
  option domain-name "eyeq.vpn";
  option domain-name-servers *******; 
  option classless-routes       24, 192,168,100, 10,20,0,2,
				24, 192,168,50, 10,20,0,2;
}


############################# KFC ###########################################



zone 21.10.in.addr.arpa. {
  primary **************;
}

subnet ********* netmask ************* {

  max-lease-time 86400;
  range ********* ***********;

  option subnet-mask *************;
  option dhcp-server-identifier *********;
  option broadcast-address ***********;
  option domain-name "eyeq.vpn";
  option domain-name-servers *******;
  option classless-routes       24, 192,168,100, 10,21,0,2,
                                24, 192,168,50, 10,21,0,2;
}





############################# CZP ###########################################

zone 27.10.in.addr.arpa. {
  primary **************;
}

subnet ********* netmask ************* {
  ddns-domainname               "eyeq.vpn";

  max-lease-time 86400;
  range ********* ***********;

  option subnet-mask *************;
  option dhcp-server-identifier *********;
  option broadcast-address ***********;
  option domain-name "eyeq.vpn";
  option domain-name-servers *******;
  option classless-routes       24, 192,168,100, 10,27,0,2,
                                24, 192,168,50, 10,27,0,2;
}


############################# STB ###########################################

zone 28.10.in.addr.arpa. {
  primary **************;
}

subnet ********* netmask ************* {
  ddns-domainname               "eyeq.vpn";

  max-lease-time 86400;
  range ********* ***********;

  option subnet-mask *************;
  option dhcp-server-identifier *********;
  option broadcast-address ***********;
  option domain-name "eyeq.vpn";
  option domain-name-servers *******;
  option classless-routes       24, 192,168,100, 10,28,0,2,
                                24, 192,168,50, 10,28,0,2;
}


############################# MNZ ###########################################

zone 29.10.in.addr.arpa. {
  primary **************;
}

subnet ********* netmask ************* {
  ddns-domainname               "eyeq.vpn";

  max-lease-time 86400;
  range ********* ***********;

  option subnet-mask *************;
  option dhcp-server-identifier *********;
  option broadcast-address ***********;
  option domain-name "eyeq.vpn";
  option domain-name-servers *******;
  option classless-routes       24, 192,168,100, 10,29,0,2,
                                24, 192,168,50, 10,29,0,2;
}


############################# ELJ ###########################################

zone 30.10.in.addr.arpa. {
  primary **************;
}

subnet ********* netmask ************* {
  ddns-domainname               "eyeq.vpn";

  max-lease-time 86400;
  range ********* ***********;

  option subnet-mask *************;
  option dhcp-server-identifier *********;
  option broadcast-address ***********;
  option domain-name "eyeq.vpn";
  option domain-name-servers *******;
  option classless-routes       24, 192,168,100, 10,30,0,2,
                                24, 192,168,50, 10,30,0,2;
}


############################# POC ###########################################

zone 32.10.in.addr.arpa. {
  primary **************;
}

subnet ********* netmask ************* {
  ddns-domainname               "eyeq.vpn";

  max-lease-time 86400;
  range ********* ***********;

  option subnet-mask *************;
  option dhcp-server-identifier *********;
  option broadcast-address ***********;
  option domain-name "eyeq.vpn";
  option domain-name-servers *******;
  option classless-routes       24, 192,168,100, 10,32,0,2,
                                24, 192,168,50, 10,32,0,2;
}


############################# CFA ###########################################

zone 22.10.in.addr.arpa. {
  primary **************;
}

subnet ********* netmask ************* {
  ddns-domainname               "eyeq.vpn";

  max-lease-time 86400;
  range ********* ***********;

  option subnet-mask *************;
  option dhcp-server-identifier *********;
  option broadcast-address ***********;
  option domain-name "eyeq.vpn";
  option domain-name-servers *******;
  option classless-routes       24, 192,168,100, 10,22,0,2,
                                24, 192,168,50, 10,22,0,2;
}

############################# TST ###########################################

zone 255.10.in.addr.arpa. {
  primary **************;
}

subnet ********** netmask ************* {
  ddns-domainname               "eyeq.vpn";

  max-lease-time 86400;
  range ********** ************;

  option subnet-mask *************;
  option dhcp-server-identifier **********;
  option broadcast-address ************;
  option domain-name "eyeq.vpn";
  option domain-name-servers *******;
  option classless-routes       24, 192,168,100, 10,255,0,2,
                                24, 192,168,50, 10,255,0,2;
}


#############################################################################

zone 50.168.192.in.addr.arpa. {
  primary **************;
}


subnet ************ netmask ************* {
  max-lease-time 86400;
  range ************* **************;
  option subnet-mask *************;
  option dhcp-server-identifier ************;
  option broadcast-address **************;
  option domain-name-servers **************;
  option domain-search          "eyeq.vpn";
  option classless-routes 	24, 192,168,100, 192,168,50,2,
				22, 10,20,0, 192,168,50,2,
				22, 10,21,0, 192,168,50,2,
				22, 10,22,0, 192,168,50,2,
				22, 10,23,0, 192,168,50,2,
				22, 10,24,0, 192,168,50,2,
				22, 10,25,0, 192,168,50,2,
				22, 10,27,0, 192,168,50,2,
				22, 10,28,0, 192,168,50,2,
				22, 10,29,0, 192,168,50,2,
				22, 10,30,0, 192,168,50,2,
				22, 10,31,0, 192,168,50,2,
				22, 10,32,0, 192,168,50,2,
				22, 10,255,0, 192,168,50,2;
}
