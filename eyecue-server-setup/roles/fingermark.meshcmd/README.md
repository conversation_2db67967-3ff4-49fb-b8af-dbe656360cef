fingermark.meshcmd
=========

Use this role to setup and register a server with AMT technology

Requirements
------------

Any pre-requisites that may not be covered by Ansible itself or the role should be mentioned here. For instance, if the role uses the EC2 module, it may be a good idea to mention in this section that the boto package is required.

Role Variables
--------------

Select the group from 
- mcd:
  meshid: "dyiCkIzEiLxiy14E"
- cfa:
  meshid: "8NzUo5UXIdWpFyQX"
- crk:
  meshid: "rhizXCzYqdDaVUo9"

You also need AMT password

Dependencies
------------

A list of other roles hosted on Galaxy should go here, plus any details in regards to parameters that may need to be set for other roles, or variables that are used from other roles.

Example Playbook
----------------

Including an example of how to use your role (for instance, with variables passed in as parameters) is always nice for users too:

    - hosts: hastingsprod
      gather_facts: no
      become: yes
      vars:
      - meshid: dyiCkIzEiLxiy14E
      - amt_password: Q1w2e3r4!
      roles:
      - fingermark.meshcmd

License
-------

BSD
