---
# tasks file for fingermark.dns-server
- name: "Installing packages"
  apt:
    name:
    - curl
    - bind9
    - dnsutils
    update_cache: yes


- name: Copying config files 1/3
  copy:
    src: db.{{ zone_name[:-1] }}
    dest: /var/cache/bind
    owner: bind

- name: Copying config files 2/3
  template:
    src: named.conf.local
    dest: /etc/bind/

- name: Copying config files 3/3
  template:
    src: named.conf.options
    dest: /etc/bind/


- name: Creating cronjob 
  shell: echo '*/5 * * * *  root  /usr/sbin/rndc sync 2>&1' > /etc/cron.d/sync-zone

- name: "Restarting Bind9 service"
  systemd:
    name: bind9
    state: restarted

- name: Disabling systemd-resolved service
  ansible.builtin.systemd:
    name: systemd-resolved
    state: stopped
    enabled: no