---
- name: "Creating certificate folder"
  ansible.builtin.file:
    path: "{{ icinga2_cert_path }}"
    owner: "nagios"
    group: "nagios"
    state: directory

- name: "Fetching master certificate"
  ansible.builtin.shell: "icinga2 pki new-cert --cn {{ ansible_hostname }} --key {{ icinga2_cert_path }}/{{ ansible_hostname }}.key --cert {{ icinga2_cert_path }}/{{ ansible_hostname }}.crt"
  when: icinga2_run_setup or icinga_force_setup

- name: "Saving master certificate!!!!"
  ansible.builtin.shell: icinga2 pki save-cert --trustedcert {{ icinga2_cert_path }}/ca.crt --host {{ icinga2_satellite_endpoint }}
  when: icinga2_run_setup or icinga_force_setup

- name: Running node setup
  ansible.builtin.command: |
    icinga2 node setup
      --cn {{ ansible_hostname }}
      --endpoint {{ icinga2_satellite_endpoint }}
      --zone {{ ansible_hostname }}
      --parent_zone {{ customer | default(ansible_hostname[3:6]) }}
      --parent_host {{ icinga2_satellite_endpoint }}
      --trustedcert {{ icinga2_cert_path }}/ca.crt
      --accept-commands
      --accept-config
      --disable-confd
  when: icinga2_run_setup or icinga_force_setup

- name: Adding Icinga2 server IP
  ansible.builtin.lineinfile:
    path: "/etc/icinga2/zones.conf"
    line: "\thost = \"{{ icinga2_satellite_endpoint }}\""
    regexp: "\thost = \"{{ icinga2_satellite_endpoint }}\""
    insertafter: "object Endpoint \"{{ icinga2_satellite_endpoint }}\" {"
  when: icinga2_run_setup or icinga_force_setup

- name: Adding Icinga2 server Port
  ansible.builtin.lineinfile:
    path: "/etc/icinga2/zones.conf"
    line: "\tport = \"5665\""
    regexp: "5665"
    insertafter: "host = \"{{ icinga2_satellite_endpoint }}\""
  when: icinga2_run_setup or icinga_force_setup
  notify: Restarting Icinga2

- name: "Setting user nagios as owner"
  ansible.builtin.file:
    path: "/var/lib/icinga2/api/"
    owner: "nagios"
    group: "nagios"
    mode: 0750
    recurse: yes

- name: Remove any whitespaces from coordinates
  ansible.builtin.set_fact:
    cleaned_coordinates: "{{ coordinates | replace(' ', '') }}"
  when: icinga2_run_setup or icinga_force_setup

- name: Triggering auto setup on Icinga2 server
  ansible.builtin.uri:
    url: https://{{ icinga2_master_node }}/config/{{ ansible_hostname }}?display_name={{ display_name | replace(" ", "%20") }}&coordinates={{ cleaned_coordinates }}
    method: POST
    user: deployer
    password: "{{ icinga2_auto_config_passwd }}"
    force_basic_auth: yes
  when: icinga2_run_setup or icinga_force_setup

- name: Creating setup confirmation file
  ansible.builtin.file:
    path: "{{ icinga2_setup_file_stat }}"
    state: touch
    mode: 0600
    owner: nagios
    group: nagios
  when: icinga2_run_setup or icinga_force_setup

- name: "Adding custom checks"
  ansible.builtin.include_tasks: custom_checks.yml

- name: "Adding custom event commands"
  ansible.builtin.include_tasks: event_commands.yml
