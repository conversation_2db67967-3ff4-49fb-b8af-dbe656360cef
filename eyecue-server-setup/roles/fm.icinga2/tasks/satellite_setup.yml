---
- name: "Creating certificate folder"
  ansible.builtin.file:
    path: "{{ icinga2_cert_path }}"
    owner: "nagios"
    group: "nagios"
    state: directory

- name: "Fetching master certificate"
  ansible.builtin.shell: "icinga2 pki new-cert --cn {{ hostname }} --key {{ icinga2_cert_path }}/{{ hostname }}.key --cert {{ icinga2_cert_path }}/{{ hostname }}.crt"
    
- name: "Saving master certificate!!!!"
  ansible.builtin.shell: icinga2 pki save-cert --trustedcert {{ icinga2_cert_path }}/ca.crt --host {{ icinga2_master_api_endpoint }}

- name: Running node setup
  ansible.builtin.command: |
    icinga2 node setup 
      --cn {{ hostname | default(ansible_hostname) }} 
      --endpoint {{ icinga2_master_node }}
      --zone {{ customer }} 
      --parent_zone master
      --parent_host {{ icinga2_master_endpoint }}
      --trustedcert {{ icinga2_cert_path }}/ca.crt 
      --accept-commands 
      --accept-config 
      --disable-confd

- name: "Adding master server IP and port (copying the right zone.conf file)"
  ansible.builtin.template:
    src: satellite_zone.conf
    dest: /etc/icinga2/zones.conf
    mode: 0644
    owner: nagios
    group: nagios
  notify: "Restarting Icinga2"
