---
- name: Installation
  ansible.builtin.include_tasks: install.yml

- name: "Checking if Icinga2 is already configured"
  ansible.builtin.stat:
    path: "/etc/icinga2/node_setup"
  register: icinga2_is_ready

- name: "Checking if Icinga2 is already configured"
  ansible.builtin.debug:
    msg: "Icinga2 is configured: {{icinga2_is_ready.stat}}"

- ansible.builtin.set_fact:
    icinga2_run_setup: true
  when: not icinga2_is_ready.stat.exists

- name: Setting up node
  ansible.builtin.include_tasks: node_setup.yml
  when: icinga2_node_setup | bool

- name: Setting up Icinga2 Satellite
  ansible.builtin.include_tasks: satellite_setup.yml
  when: icinga2_satellite_setup and (icinga2_run_setup or icinga_force_setup)
