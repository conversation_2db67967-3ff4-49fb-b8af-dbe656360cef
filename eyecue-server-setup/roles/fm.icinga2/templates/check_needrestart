#!/bin/bash

export PATH="/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games"

CRON_FILE="/etc/cron.d/needrestart"
CRON_TASK="{{ maintenance_window }}"
NEXTRESTART=""
NEEDRESTART=$(sudo /usr/sbin/needrestart -p)

CMD_OUTPUT=$?

if [ $CMD_OUTPUT -gt 0 ]; then
  if [ $CMD_OUTPUT -eq 1 ]; then
    sudo /usr/sbin/needrestart -q -r a
    sudo systemctl restart unattended-upgrades.service
    NEXTRESTART="needrestart: Daemons have been restarted"
  else
    /usr/bin/logger "needrestart: Server needs to be rebooted:\n"
    /usr/bin/logger "${NEEDRESTART}\n"
    /usr/bin/logger "needrestart: Schedulling reboot..."

    sudo touch ${CRON_FILE}
    echo "$CRON_TASK > /dev/null 2>&1" | sudo tee ${CRON_FILE} >/dev/null
    NEXTRESTART="needrestart: Next reboot will be on Tuesday 3AM local time"
  fi
else
  if [ -f ${CRON_FILE} ]; then
    echo "# $CRON_TASK  > /dev/null 2>&1" | sudo tee ${CRON_FILE} >/dev/null
  fi
  NEXTRESTART="needrestart: No need to restart"
fi
/usr/bin/logger "${NEXTRESTART}"
echo -e "${NEXTRESTART}\n" && sudo /usr/sbin/needrestart -p
