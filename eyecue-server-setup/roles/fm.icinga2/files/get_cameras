#!/bin/bash

#Interfaces are filtered by the word "en"

INTERFACES=($(ifconfig | awk '/^en/{print $1}' | sed -e "s/://"))
SUBNETS=()
CAMERAS=()
CONTINUE=$1

if [ ! -z $CONTINUE ]; then
  if [ $CONTINUE == "-y" ]; then
    CONTINUE="y"
  fi
else
  CONTINUE="N"
fi


if [ ! -f /var/cache/icinga2/cameras_ip.txt ]; then
  FILE_EXISTS=1
else
  FILE_EXISTS=0
fi

if [ $FILE_EXISTS -eq 0 -a $CONTINUE == "N" ]; then
  echo "File exists, continue anyway? (y/N)"
  read CONTINUE
  if [ -z $CONTINUE ]; then
    CONTINUE="N"
  fi
else
  CONTINUE="y"
fi

if [ $CONTINUE == "y" ]; then
  for interface in "${INTERFACES[@]}"; do
    SUBNETS+=($(route -n |  grep "$interface" | awk {'print $1'} | grep -v 0.0.0.0));
  done

  for subnet in "${SUBNETS[@]}"; do
    #echo "Checking $subnet";
    CAMERAS+=($(nmap "$subnet"/24 -p 554  --open -oG - | awk '/Up$/{print $2}';))
  done

  echo ${CAMERAS[@]} > /var/cache/icinga2/cameras_ip.txt
else
  echo "Bye!"
fi