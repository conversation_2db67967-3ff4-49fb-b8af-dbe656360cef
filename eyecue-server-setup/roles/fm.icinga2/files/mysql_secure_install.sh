#!/bin/bash

// Not required in actual script
MYSQL_ROOT_PASSWORD="q1w2e3r4!"

if [ ${MYSQL_ROOT_PASSWORD} == "" ]; then
    echo "Please enter the root password. Exiting..."
    exit 1
fi

SECURE_MYSQL=$(expect -c "

set timeout 10
spawn mysql_secure_installation

expect \"Enter current password for root (enter for none):\"
send \"$MYSQL_ROOT_PASSWORD\r\"

expect \"Change the root password?\"
send \"n\r\"

expect \"Remove anonymous users?\"
send \"y\r\"

expect \"Disallow root login remotely?\"
send \"y\r\"

expect \"Remove test database and access to it?\"
send \"y\r\"

expect \"Reload privilege tables now?\"
send \"y\r\"

expect eof
")

echo "$SECURE_MYSQL"

apt -y purge expect