#!/bin/bash

COMMENT="Pending..."
USER="auto-ack"
PASSWORD="jbKzifBLso2g"
SERVER="icinga2.eyecue.fingermark.co.nz"


curl -k -s -u $USER:$PASSWORD -H 'Accept: application/json' \
  -X POST 'https://'$SERVER':5665/v1/actions/acknowledge-problem' \
  -d '{ "type": "Service", "filter": "match(\"*needrestart\", service.name) && match(\"'"*$HOSTNAME*"'\", host.name) && (service.state==2 || service.state==1 ) && service.acknowledgement!=1", "author": "Automatic", "comment": '\"$COMMENT\"', "notify": false, "pretty": true }'
