#!/usr/bin/env python3
import sys
import argparse
import docker

def check_container(container_name):
    client = docker.from_env()

    try:
        container = client.containers.get(container_name)
        status = container.status
        if status == "running":
            print(f"Container {container_name} is running.")
            return 0  # OK
        else:
            return 2  # CRITICAL
    except docker.errors.NotFound:
        return 3  # Unknown

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Check the status of a Docker container.")
    parser.add_argument("--container-name", "-cn", dest="container_name", help="Name of the Docker container to check")

    args = parser.parse_args()

    try:
        exit_code = check_container(args.container_name)
        sys.exit(exit_code)
    except Exception as e:
        print(f"An error occurred: {e}")
        sys.exit(3)  # Unknown status
