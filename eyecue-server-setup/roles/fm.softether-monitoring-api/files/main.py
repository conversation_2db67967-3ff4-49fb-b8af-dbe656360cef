# Documentation https://grafana.com/grafana/plugins/marcus<PERSON><PERSON>-json-datasource/

def parse_output(output):

    sessions_dict = {'sessions': []}
    for item in output:
        session = {}
        sessions_list = item.split('\n')
        sessions_list.pop(len(sessions_list)-1)

        for a in range(1,len(sessions_list)):
            key_name = sessions_list[a].split('|')[0].strip().replace(' ', '_')
            key_value = sessions_list[a].split('|')[1].strip()
            if key_name == 'Transfer_Bytes' or key_name == 'Transfer_Packets': 
              key_value = int(key_value.replace(',',''))
            session[key_name] = key_value
        sessions_dict['sessions'].append(session)
    
    return (sessions_dict)

def get_sessions(vpn_server, passwd, admin_hub):
    import subprocess

    SOFTETHER_CMD = '/usr/local/vpnclient/vpncmd'
    STRING_SEPARATOR = '----------------+-------------------------------------------------------'
    BEGINNING_BYTES = 643
    ENDING_BYTES = -38

    output = subprocess.run([SOFTETHER_CMD, '/SERVER', vpn_server, f'/PASSWORD:{passwd}', f'/ADMINHUB:{admin_hub}', '/CMD', 'SessionList'], capture_output=True)
    sessions_raw = output.stdout[BEGINNING_BYTES:][:ENDING_BYTES].decode('utf8').split(STRING_SEPARATOR)

    return(parse_output(sessions_raw))


from loguru import logger
import logging
from typing import Optional
from fastapi import BackgroundTasks, HTTPException, FastAPI, Request

##### Get the logging level based on uvicorn level
def log_parser(msg):
    import json
    import sys
    print(json.loads(msg)['text'], file=sys.stderr,)

def get_log_level(level: int):
    return({
        10: 'DEBUG',
        20: 'INFO',
        30: 'WARNING',
        40: 'ERROR',
        50: 'CRITICAL'
    }).get(level, 20)

uvi_logger = logging.getLogger(__name__)

##### Reset loguru logger settings and set log level based on uvicorn log level
logger.remove()
logger.add(log_parser, level=get_log_level(uvi_logger.getEffectiveLevel()), serialize=True)

app = FastAPI()


@app.get('/', status_code=200)
async def status():       
    return 'OK'


@app.get('/sessions', status_code=200)
async def sessions(vpn_server: Optional[str] = 'vpn.eyecue.fingermark.co.nz:443', admin_hub: Optional[str] = 'FM-TEAM'):
    sessions = get_sessions(vpn_server, 'g2JEHv6UxqEDASFmDrgw', admin_hub)
    return sessions


