# fm.network-cli

This role installs the EyeCue Network CLI. The CLI is used to interact with the Netplan.
  - https://bitbucket.org/fingermarkltd/eyecue-network-cli/src/main/

Role Variables
--------------

| Variable Name                | Description                                                                 |
|------------------------------|-----------------------------------------------------------------------------|
| `network_cli_repo`           | The name of the repository for the eyecue-network-cli package.              |
| `network_cli_version`        | The version of the eyecue-network-cli package to be installed.              |
| `network_cli_virtualenv`     | The path to the virtual environment where the package will be installed.    |
| `network_cli_script_name`    | The name of the script to be linked to bin.                                 |
| `network_cli_bb_access_token`| The Bitbucket access token used for authentication (encrypted with Ansible Vault). |

Example Playbook
----------------

```yaml
- hosts: servers
  tasks:
    - name: Install eyecue-network-cli
      ansible.builtin.import_role:
        name: fm.network-cli

```
