---
- name: Run apt-get update
  apt: 
    update_cache: yes
    cache_valid_time: 10800

- name: Install clamav
  apt: 
    name: ['clamav', 'clamav-base', 'clamav-freshclam']
    state: present
  notify:
    - start freshclam

- name: Create quarantine directory
  file:
    path: "{{ quarantine_directory }}"
    state: directory
    mode: 0750

- name: Add clamscan to cron
  lineinfile:
    path: /etc/crontab
    regexp: 'clamscan'
    line: 0 4 * * * root /usr/bin/clamscan --move="{{ quarantine_directory }}" --recursive --quiet --stdout --infected --exclude-dir="^/sys|/media|/var/lib/docker" --max-scansize=500M /var/lib > /tmp/clamav_result.log; cat /tmp/clamav_result.log | grep -v "LibClamAV Warning" | mail -E -s "Clamscan `hostname` report" root
    state: absent