---
- name: Change root password
  user: 
    name: root
    password: "{{ root_password | password_hash(salt=root_password_salt) }}"
    update_password: always

- name: Install recomended software
  apt:
    name: ['needrestart', 'debsecan', 'debsums', 'auditd', 'acct']
    state: present
    update_cache: yes

- name: Removing software
  apt:
    name: ['lynis', 'clamav', 'fail2ban']
    state: absent

- name: Remove log locations from /var/lib/logrotate/status
  ansible.builtin.lineinfile:
    path: /var/lib/logrotate/status
    state: absent
    regexp: '^"/var/log/(fail2ban\.log|clamav/freshclam\.log)"'

- name: Remove fail2ban custom config file
  ansible.builtin.file:
    dest: /etc/fail2ban/jail.d/custom.conf
    state: absent

- name: Remove fail2ban file
  ansible.builtin.file:
    path: /etc/logrotate.d/fail2ban
    state: absent

- name: Remove clamav-freshclam file
  ansible.builtin.file:
    path: /etc/logrotate.d/clamav-freshclam
    state: absent

- name: Disable debsecan daily notifications
  ansible.builtin.file:
    dest: /etc/cron.d/debsecan
    state: absent


- name: Remove Lynis from Cron
  ansible.builtin.lineinfile:
    path: /etc/crontab
    regexp: 'lynis'
    line: '0 7 * * 1 root /usr/sbin/lynis -Q --cronjob | grep warning | grep -v info | mail -E -s "Lynis report for `hostname`" root'
    state: absent

- name: Copying auditd rules
  ansible.builtin.copy:
    src: audit.rules
    dest: /etc/audit/rules.d/audit.rules
  notify: Restart auditd service


- name: OSQuery
  ansible.builtin.include_tasks: osquery.yml
  when: osquery_install