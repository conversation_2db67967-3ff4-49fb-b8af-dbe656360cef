---

- name: Add <PERSON>bernetes APT GPG key
  when: ansible_os_family == "Debian"
  apt_key:
    url: https://packages.cloud.google.com/apt/doc/apt-key.gpg
    state: present

- name: Read Ubuntu version from /etc/issue file
  shell: cat /etc/issue | grep -oP '(?<=Ubuntu\s)\d+'
  register: ubuntu_issue
  changed_when: false

- name: Extract Ubuntu version
  set_fact:
    ubuntu_version: "{{ ubuntu_issue.stdout }}"

- name: Print out ubuntu_version
  debug:
    msg: "{{ubuntu_version}}"

- name: Add Kubernetes APT repository Xenial
  when: 
    - ansible_os_family == "Debian"
    - ubuntu_version < '22'
  apt_repository:
    repo: deb [trusted=yes] https://fingermark-infra-apt-repo.s3.ap-southeast-2.amazonaws.com kubernetes-xenial main
    state: present
    filename: 'kubernetes'

- name: Add Kubernetes APT repository Jammy
  when: 
    - ansible_os_family == "Debian"
    - ubuntu_version >= '22'      
  apt_repository:
    repo: deb [trusted=yes] https://fingermark-infra-apt-repo-jammy.s3.ap-southeast-2.amazonaws.com kubernetes-jammy main
    state: present
    filename: 'kubernetes'

# - name: Add Kubernetes APT repository
#   when: ansible_os_family == "Debian"
#   apt_repository:
#     repo: deb http://apt.kubernetes.io/ kubernetes-xenial main
#     state: present
#     filename: 'kubernetes'

- name: Add Kubernetes yum repository
  when: ansible_os_family == "RedHat"
  yum_repository:
    name: Kubernetes
    description: Kubernetes Repository
    file: kubernetes
    baseurl: http://yum.kubernetes.io/repos/kubernetes-el7-x86_64
    enabled: yes
    gpgcheck: no

- name: Install kubernetes packages (RHEL/CentOS)
  when: ansible_os_family == "RedHat"
  yum:
    name: "{{ pkgs }}"
    update_cache: yes

- name: Install kubernetes packages (Debian/Ubuntu)
  when: ansible_os_family == "Debian"
  apt:
    name: "{{ pkgs }}"
    update_cache: yes
