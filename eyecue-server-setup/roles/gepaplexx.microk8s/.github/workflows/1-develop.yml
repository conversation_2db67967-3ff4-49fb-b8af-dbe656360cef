---
name: <PERSON><PERSON><PERSON>

'on':
  push:
    branches:
      - develop
  workflow_dispatch:

defaults:
  run:
    working-directory: gepaplexx.microk8s

jobs:
  pre-commit:
    name: pre-commit
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v2

      - name: Set up Python 3
        uses: actions/setup-python@v2
        with:
          python-version: '3.x'

      - name: Run pre-commit
        uses: pre-commit/action@v2.0.3

  super-linter:
    name: Gith<PERSON>
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v2

      - name: Lint Code Base with github super-linter
        uses: github/super-linter@v4
        env:
          VALIDATE_ALL_CODEBASE: true
          FILTER_REGEX_EXCLUDE: '.*.md'
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          VALIDATE_JSCPD: false
          VALIDATE_GITHUB_ACTIONS: false

  molecule:
    name: Molecule
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: gepaplexx.microk8s
    strategy:
      matrix:
        distro:
          - ubuntu2004
      fail-fast: false

    steps:
      - name: Checkout Code
        uses: actions/checkout@v2
        with:
          path: gepaplexx.microk8s

      - name: Set up Python 3
        uses: actions/setup-python@v2
        with:
          python-version: 3.x

      - name: Install Dependencies
        run: pip3 install -r requirements.txt

      - name: Run Molecule tests
        run: molecule test
        env:
          PY_COLORS: 1
          ANSIBLE_FORCE_COLOR: 1
          MOLECULE_DISTRO: ${{ matrix.distro }}

  pull-request:
    name: Pull Request
    runs-on: ubuntu-latest
    needs:
      - pre-commit
      - super-linter
      - molecule
    steps:
      - name: Checkout Code
        uses: actions/checkout@v2

      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v3
        with:
          branch: main
          token: ${{ secrets.PAT }}

  notify:
    name: Slack Notifications
    runs-on: ubuntu-latest
    if: ${{ failure() }}
    needs:
      - pull-request
    steps:
      - name: Notify Slack channel on failure
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_ICON: https://github.com/ckaserer/logos/raw/master/ansible.png
          SLACK_USERNAME: ${{ github.repository }}
          SLACK_COLOR: '#ff0033'
          SLACK_FOOTER: ''
...
