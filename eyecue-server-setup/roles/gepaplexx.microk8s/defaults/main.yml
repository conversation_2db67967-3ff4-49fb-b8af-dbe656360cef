---
## Cache update time for apt module
microk8s_cache_valid_time: 3600

## version management
microk8s_disable_snap_autoupdate: false

## plugin configuration
# microk8s_plugins:
#   dashboard: false        # The Kubernetes dashboard
#   dns: false              # CoreDNS
#   helm3: false            # Helm 3 - Kubernetes package manager
#   host-access: false      # Allow Pods connecting to Host services smoothly
#   ingress: false          # Ingress controller for external access
#   metrics-server: false   # K8s Metrics Server for API access to service
#                           # metrics
#   rbac: false             # Role-Based Access Control for authorisation
#   registry: false         # Private image registry exposed on localhost:32000
#   storage: false          # Storage class; allocates storage from host
#                           # directory
#   ambassador: false       # Ambassador API Gateway and Ingress
#   cilium: false           # SDN, fast with full network policy
#   fluentd: false          # Elasticsearch-Fluentd-Kibana logging and
#                           # monitoring
#   gpu: false              # Automatic enablement of Nvidia CUDA
#   helm: false             # Helm 2 - the package manager for Kubernetes
#   istio: false            # Core Istio service mesh services
#   jaeger: false           # Kubernetes Jaeger operator with its simple config
#   knative: false          # The Knative framework on Kubernetes.
#   kubeflow: false         # Kubeflow for easy ML deployments
#   linkerd: false          # Linkerd is a service mesh for Kubernetes
#   #                         and other frameworks
#   metallb: false          # Loadbalancer for your Kubernetes cluster
#   multus: false           # Multus CNI enables attaching multiple network
#   #                         interfaces to pods
#   prometheus: false       # Prometheus operator for monitoring and logging
#   traefik: false

microk8s_registry_size: 20Gi

microk8s_dns_servers:
  - *******
  - *******

## microk8s_users to make members of microk8s group
microk8s_users: []

## enable high-availability?
microk8s_enable_ha: false

## hostgroup whose members will form high-availability cluster
microk8s_group_ha: "microk8s_HA"

# regex to select IP address for joining nodes in HA setup
microk8s_ip_regex_ha: "([0-9]{1,3}[\\.]){3}[0-9]{1,3}"

# change this to useable ip range or ip addresses (refer to metallb documentation) for metallb to use
microk8s_metallb_ip_range: "********/16"
...
