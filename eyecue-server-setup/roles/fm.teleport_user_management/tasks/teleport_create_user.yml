---
- name: Adding user in teleport
  shell: "tctl users add --roles {{ teleport_role }} {{ teleport_user }} --logins {{ teleport_user }}"
  register: teleport_user_output

- name: Teleport user add output
  debug: 
    msg: 
      - "**********************" 
      - "Invite code for the user {{ teleport_user }}:"
      - "{{ teleport_user_output.stdout_lines[1] }}"
      - "**********************" 

- name: List of user in teleport
  shell: "tctl users ls"
  register: teleport_lsuser_output

- name: Teleport list of suers
  debug: 
    msg: 
      - "{{ teleport_lsuser_output.stdout_lines }}"
