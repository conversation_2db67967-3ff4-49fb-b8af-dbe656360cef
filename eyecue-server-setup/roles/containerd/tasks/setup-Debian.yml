---
- name: Ensure dependencies are installed.
  ansible.builtin.apt:
    name:
      - apt-transport-https
      - ca-certificates
      - gnupg2
    state: present

- name: Add Docker apt key.
  ansible.builtin.apt_key:
    url: "{{ docker_apt_gpg_key }}"
    id: "{{ docker_apt_key_id }}"
    state: present
  register: add_repository_key
  ignore_errors: "{{ docker_apt_ignore_key_error }}"

- name: Ensure curl is present (on older systems without SNI).
  ansible.builtin.package: name=curl state=present
  when: add_repository_key is failed

- name: Add Docker apt key (alternative for older systems without SNI).
  ansible.builtin.shell: >
    curl -sSL {{ docker_apt_gpg_key }} | sudo apt-key add -
  when: add_repository_key is failed

- name: Add Docker repository.
  ansible.builtin.apt_repository:
    repo: "{{ docker_apt_repository }}"
    state: present
    update_cache: true
