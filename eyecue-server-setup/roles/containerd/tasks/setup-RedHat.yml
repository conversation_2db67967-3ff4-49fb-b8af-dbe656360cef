---
- name: Add Docker GPG key.
  ansible.builtin.rpm_key:
    key: "{{ docker_yum_gpg_key }}"
    state: present

- name: Add Docker repository.
  ansible.builtin.get_url:
    url: "{{ docker_yum_repo_url }}"
    dest: '/etc/yum.repos.d/docker-ce.repo'
    owner: root
    group: root
    mode: 0644

- name: Configure Docker Nightly repo.
  community.general.ini_file:
    dest: '/etc/yum.repos.d/docker-ce.repo'
    section: 'docker-ce-nightly'
    option: enabled
    value: '{{ docker_yum_repo_enable_nightly }}'
    mode: 0644

- name: Ensure container-selinux is installed.
  ansible.builtin.package:
    name: container-selinux
    state: present
