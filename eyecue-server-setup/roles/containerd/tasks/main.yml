---
- include_tasks: setup-RedHat.yml
  when: ansible_os_family == 'RedHat'

- include_tasks: setup-Debian.yml
  when: ansible_os_family == 'Debian'

- name: Ensure containerd is installed.
  ansible.builtin.package:
    name: "{{ containerd_package }}"
    state: "{{ containerd_package_state }}"
    allow_downgrade: "{{ containerd_allow_downgrade }}"

- name: Ensure containerd is started and enabled at boot.
  ansible.builtin.service:
    name: containerd
    state: "{{ containerd_service_state }}"
    enabled: "{{ containerd_service_enabled }}"

- include_tasks: config_setup.yml
  when: containerd_config_setup