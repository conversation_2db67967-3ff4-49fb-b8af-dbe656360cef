---
- name: Converge
  hosts: all
  become: true

  pre_tasks:
    - name: Update apt cache.
      ansible.builtin.apt: update_cache=yes cache_valid_time=600
      when: ansible_os_family == 'Debian'

    - name: Wait for systemd to complete initialization.  # noqa 303
      ansible.builtin.command: systemctl is-system-running
      register: systemctl_status
      until: >
        'running' in systemctl_status.stdout or
        'degraded' in systemctl_status.stdout
      retries: 30
      delay: 5
      when: ansible_service_mgr == 'systemd'
      changed_when: false
      failed_when: systemctl_status.rc > 1

  roles:
    - role: geerlingguy.containerd
