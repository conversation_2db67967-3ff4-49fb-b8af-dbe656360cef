---
# Package options.
# containerd_package: containerd.io
# containerd_allow_downgrade: false
containerd_package: containerd.io=1.6.19-1
containerd_config_setup: false
containerd_allow_downgrade: true
containerd_package_state: present
# Service options.
containerd_service_state: started
containerd_service_enabled: true

# Write a default containerd config.toml file.
containerd_config_default_write: true

# Set systemd as cgroup driver in config.toml
# Only use with containerd_config_default_write: true
containerd_config_cgroup_driver_systemd: false

# Used only for Debian/Ubuntu. Switch 'stable' to 'nightly' if needed.
docker_apt_release_channel: stable
docker_apt_key_id: 9DC858229FC7DD38854AE2D88D81803C0EBFCD88
docker_apt_arch: '{{ (ansible_architecture == "aarch64") | ternary("arm64", "amd64") }}'
docker_apt_repository: "deb [arch={{ docker_apt_arch }}] https://download.docker.com/linux/{{ ansible_distribution | lower }} {{ ansible_distribution_release }} {{ docker_apt_release_channel }}"
docker_apt_ignore_key_error: true
docker_apt_gpg_key: https://download.docker.com/linux/{{ ansible_distribution | lower }}/gpg

# Used only for RedHat/CentOS/Fedora.
docker_yum_repo_url: https://download.docker.com/linux/{{ (ansible_distribution == "Fedora") | ternary("fedora","centos") }}/docker-ce.repo
docker_yum_repo_enable_nightly: '0'
docker_yum_gpg_key: https://download.docker.com/linux/centos/gpg
