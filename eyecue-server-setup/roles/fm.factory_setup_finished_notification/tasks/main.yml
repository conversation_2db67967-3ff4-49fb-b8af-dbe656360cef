- name: Send notification message to Slack channel
  slack:
    token: "{{ slack_token }}"
    channel: "{{ slack_channel }}"
    blocks:
      - type: section
        text:
          type: mrkdwn
          text: |-
            *:white_check_mark: Playbook - Factory Server Setup Completed*
            display_name=`{{ display_name | default('Unknown') }}`
            inventory_hostname=`{{ inventory_hostname | default('Unknown') }}`
            ansible_hostname=`{{ ansible_hostname | default('unknown') }}`
            ansible_port=`{{ ansible_port | default('unknown') }}`
      - type: divider
  when: slack_notification
