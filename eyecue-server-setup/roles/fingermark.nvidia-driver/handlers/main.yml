---
# The ansible.builtin.reboot module is not compatible with connection=local so using command instead
- name: Reboot on driver install
  ansible.builtin.command: "/usr/sbin/reboot"
  async: 0
  poll: 0
  timeout: 300

- name: Reboot on driver install using the reboot module
  reboot:
    msg: "Reboot initiated by Ansible"
    connect_timeout: 5
    reboot_timeout: 600
    pre_reboot_delay: 0
    post_reboot_delay: 60
    test_command: whoami
