---
- name: Check if system has NVIDIA GPU
  shell: "lspci | grep -i nvidia"
  register: nvidia_gpu_check
  ignore_errors: true
  changed_when: false

- name: Fail if no NVIDIA GPU found
  fail:
    msg: "No NVIDIA GPU detected in the system"
  when: nvidia_gpu_check.rc != 0

# Include variables and define needed variables.
# eg: vars/Ubuntu.yml
- name: Include OS-specific variables
  include_vars: "{{ ansible_distribution }}.yml"

- name: "Checking if secure boot has been disabled"
  ansible.builtin.command: "mokutil --sb-state"
  register: sec_boot_state
  ignore_errors: true
  changed_when: false

- name: Warn about secure boot
  debug:
    msg: "Warning: Secure Boot is enabled. This might prevent NVIDIA drivers from loading."
  when: "'enabled' in sec_boot_state.stdout | default('')"

- ansible.builtin.debug:
    msg: "{{  sec_boot_state.stdout | default('') }}"

# Setup/install tasks.
# eg: tasks/setup-Ubuntu.yml
- ansible.builtin.include_tasks: "setup-{{ ansible_distribution }}.yml"
