---
- name: Check if NVIDIA driver is already installed
  shell: "nvidia-smi --query-gpu=driver_version --format=csv,noheader"
  register: current_driver
  ignore_errors: true
  changed_when: false

- name: Purge existing NVIDIA installations if requested
  apt:
    name: "nvidia-*"
    state: absent
    purge: yes
  when: nvidia_driver_purge_existing | bool

- name: Create nvidia_driver_ppa_state variable
  set_fact:
    nvidia_driver_ppa_state: "{{ 'absent' if not nvidia_driver_use_ppa else 'present' }}"

- name: Add apt repository
  apt_repository: 
    repo: "{{ nvidia_driver_apt_repository }}" 
    state: "{{ nvidia_driver_ppa_state }}"
    filename: "gpu_driver"
  register: repo_status
  retries: 5
  delay: 10
  until: repo_status is succeeded

- name: Update apt cache if repo changed
  apt:
    update_cache: yes
  when: repo_status.changed

- name: Search for latest package
  shell: |
    apt-cache search --names-only "nvidia-" \
    | grep "nvidia-driver-[[:digit:]]\+ " \
    | awk '{print $1}' \
    | uniq \
    | sort -r \
    | head -1
  register: _nv_latest_package
  when: nvidia_driver_version == 'latest'
  changed_when: false

- name: Set driver package name
  set_fact:
    nvidia_driver_package: >-
      {{ _nv_latest_package.stdout if nvidia_driver_version == 'latest'
         else 'nvidia-driver-' ~ nvidia_driver_version }}

- name: Install NVIDIA driver
  apt: 
    name: "{{ nvidia_driver_package }}" 
    state: present
  notify: Reboot on driver install
  register: driver_install

- name: Notify Reboot on driver install if remote and driver changed
  meta: noop
  when:
    - driver_install is changed
    - nvidia_driver_reboot_if_changed | bool
    - not nvidia_driver_skip_reboot | bool
    - ansible_connection != 'local'
  notify:
    - "Reboot on driver install"

- name: Notify Reboot on driver install using the reboot module if local and driver changed
  meta: noop
  when:
    - driver_install is changed
    - nvidia_driver_reboot_if_changed | bool
    - not nvidia_driver_skip_reboot | bool
    - ansible_connection == 'local'
  notify:
    - "Reboot on driver install using the reboot module"
