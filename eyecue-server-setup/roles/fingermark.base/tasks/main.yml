---
- name: Upgrade existing software packages
  apt:
    upgrade: full
    update_cache: yes
  when: pkg_upgrade

- name: Install default system packages
  apt:
    pkg: "{{ item }}"
    update_cache: yes
    state: present
  with_items: "{{ default_system_packages }}"

- name: Install additional system packages
  apt:
    pkg: "{{ item }}"
    update_cache: yes
    state: present
  with_items: "{{ additional_system_packages }}"
  when: additional_system_packages is defined
