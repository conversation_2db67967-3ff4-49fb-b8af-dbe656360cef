---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: monitor-prometheus-operato-rabbitmq-monitor
  namespace: monitoring
  labels:
    app: prometheus-rabbitmq-exporter
    k8s-app: rabbitmq-monitor
    release: monitor
spec:
  selector:
    matchLabels:
      app: prometheus-rabbitmq-exporter
  namespaceSelector:
    matchNames:
    - nmp-{{ ansible_hostname }}
  endpoints:
    - port: publish
      path: /metrics
      interval: 30s
      honorLabels: true
