---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: monitor-prometheus-operato-gpu-monitor
  namespace: monitoring
  labels:
    app: prometheus-gpu-exporter
    k8s-app: gpu-monitor
    release: monitor
spec:
  selector:
    matchLabels:
      app: dcgm-exporter
  namespaceSelector:
    matchNames:
      - nmp-{{ ansible_hostname }}
  endpoints:
    - port: publish
      path: /metrics
      interval: 30s
      honorLabels: true
