---
- name: Uninstall Prometheus Operator
  ansible.builtin.include_tasks: uninstall.yml
  when: prometheus_operator_uninstall or (not prometheus_operator_present)

- name: Install Node Exporter standalone version
  ansible.builtin.include_tasks: standalone_node_exporter.yml
  when: (node_exporter_standalone_version) and ( prometheus_operator_present )

- name: Starting Prometheus Operator installation...
  ansible.builtin.include_tasks: prometheus_operator.yml
  when: prometheus_operator_present

- name: Install Exporters
  include_tasks: exporters.yml
  when: prometheus_operator_present

- name: Install Thanos
  include_tasks: thanos.yml
  when: (thanos_upgrade) or (thanos_sidecar_install)
