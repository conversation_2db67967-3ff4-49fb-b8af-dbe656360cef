---
# https://docs.ansible.com/ansible/latest/collections/kubernetes/core/helm_module.html#requirements

- name: Removing Prometheus Operator
  kubernetes.core.helm:
    name: monitor
    state: absent
    wait: true
    kubeconfig: "{{ kubeconfig_location }}"
  when: prometheus_operator_uninstall
  ignore_errors: true

- name: wait for calico-kube-controller pods to start
  kubernetes.core.k8s_info:
    kind: Pod
    wait: true
    label_selectors:
      - k8s-app=calico-kube-controllers
    namespace: kube-system
    kubeconfig: "{{ kubeconfig_location }}"
    wait_sleep: 5
    wait_timeout: 200
    wait_condition:
      type: Ready
      status: "True"

- name: wait for gpu operator pod to start
  kubernetes.core.k8s_info:
    kind: Pod
    wait: true
    label_selectors:
      - app.kubernetes.io/component=gpu-operator
      - app=gpu-operator
    namespace: gpu-operator-resources
    kubeconfig: "{{ kubeconfig_location }}"
    wait_sleep: 5
    wait_timeout: 400
    wait_condition:
      type: Ready
      status: "True"

- name: wait for reliant gpu operator pod to start
  kubernetes.core.k8s_info:
    kind: Pod
    wait: true
    label_selectors:
      - app.kubernetes.io/part-of=gpu-operator
      - app=nvidia-operator-validator
    namespace: gpu-operator-resources
    kubeconfig: "{{ kubeconfig_location }}"
    wait_sleep: 5
    wait_timeout: 400
    wait_condition:
      type: Ready
      status: "True"

- name: Checking if microk8s deployed the default dcgm exporter
  shell: |
    {{ kubectl }} get ds -n gpu-operator-resources -o custom-columns=":metadata.name" | grep nvidia-dcgm-exporter
  register: default_dcgm_exporter

- name: Checking if new dcgm exporter was deployed before
  shell: |
    OUTPUT={{ kubectl }} get ds -n gpu-operator-resources -o custom-columns=":metadata.name" | grep '^dcgm-exporter'
    echo $OUTPUT
  when: default_dcgm_exporter.stdout == 'nvidia-dcgm-exporter'
  register: new_dcgm_exporter

- name: Copy dcgm-exporter template
  template:
    src: dcgm-exporter.yaml
    dest: /tmp/
    force: yes
  when: (default_dcgm_exporter.stdout == 'nvidia-dcgm-exporter' and new_dcgm_exporter.stdout == '') or (force_deploy_new_dcgm_exporter)

# Current dcgm exporter of the default gpu addon on microk8s is flaky and crashes sometimes (snap channel 1.22 and 1.24).
# So scale down this daemonset and replacing it with a working version.
- name: re-deploy dcgm exporter
  shell: |
    # Once ready scale down the default nvidia-dcgm-exporter as it will keep crashing otherwise
    {{ kubectl }} -n gpu-operator-resources patch daemonset nvidia-dcgm-exporter -p '{"spec": {"template": {"spec": {"nodeSelector": {"non-existing": "true"}}}}}'
    # apply the working version of the dcgm exporter
    {{ kubectl }} apply -n gpu-operator-resources -f /tmp/dcgm-exporter.yaml
  when: (default_dcgm_exporter.stdout == 'nvidia-dcgm-exporter' and new_dcgm_exporter.stdout == '') or (force_deploy_new_dcgm_exporter)

- name: Installing Helm Chart Prometheus' Repository
  kubernetes.core.helm_repository:
    name: prometheus-community
    repo_url: "https://prometheus-community.github.io/helm-charts"

- name: Installing Helm Chart Official Repository
  kubernetes.core.helm_repository:
    name: stable
    repo_url: "{{ helm_stable_repo }}"

# Force deleting the monitoring namespace would result in some webhooks to be prevailing which would cause an error on re-installation.
# Use helm for a clean removal of the monitor or else look out for the following webhooks:
# kubectl delete MutatingWebhookConfiguration monitor-kube-prometheus-st-admission
# kubectl delete validatingwebhookconfigurations.admissionregistration.k8s.io monitor-kube-prometheus-st-admission

- name: Install Prometheus-Operator
  kubernetes.core.helm:
    timeout: 15m0s # increase helm module timeout in case image pulls take longer.
    release_name: monitor
    release_namespace: monitoring
    chart_ref: prometheus-community/kube-prometheus-stack
    chart_version: "{{ prometheus_chart_version }}"
    create_namespace: yes
    update_repo_cache: yes
    kubeconfig: "{{ kubeconfig_location }}"
    release_values:
      # https://github.com/prometheus-community/helm-charts/blob/main/charts/kube-prometheus-stack/values.yaml
      grafana:
        enabled: false
      nodeExporter:
        enabled: "{{ prom_setup_node_exporter }}"
      alertmanager:
        enabled: "{{ prom_enable_alert_manager }}"
      coreDns:
        enabled: "{{ prom_enable_coredns }}"
      kubeApiServer:
        enabled: "{{ prom_enable_kube_api_server }}"
      kubelet:
        enabled: "{{ prom_enable_kubelet }}"
      kubeControllerManager:
        enabled: "{{ prom_enable_kube_controller_manager }}"
      kubeProxy:
        enabled: "{{ prom_enable_kube_proxy }}"
      kubeEtcd:
        enabled: "{{ prom_enable_kube_etcd }}"
      kubeScheduler:
        enabled: "{{ prom_enable_kube_scheduler }}"
      kubeStateMetrics:
        enabled: "{{ prom_enable_kube_state_metrics }}"
      prometheusOperator:
        kubeletService:
          enabled: "{{ prom_enable_kubelet }}"
      prometheus:
        service:
          type: NodePort
        prometheusSpec:
          serviceMonitorSelectorNilUsesHelmValues: false
          externalUrl: "{{ ansible_hostname }}.eyeq.vpn:30090"
          externalLabels:
            cluster: "{{ ansible_hostname }}"
          additionalScrapeConfigs:
            - job_name: gpu-metrics
              scrape_interval: 1s
              metrics_path: /metrics
              scheme: http
              kubernetes_sd_configs:
                - role: endpoints
                  namespaces:
                    names:
                      - "{{ dcgm_exporter_namespace }}"
              relabel_configs:
                - source_labels: [__meta_kubernetes_pod_node_name]
                  action: replace
                  target_label: kubernetes_node
