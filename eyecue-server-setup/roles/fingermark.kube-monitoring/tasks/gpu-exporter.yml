---
- name: Tag the cluster as NVIDIAGPU
  ansible.builtin.shell: "{{ kubectl }} label nodes $({{ kubectl }} get nodes | grep -v NAME | awk '{print $1}') hardware-type=NVIDIAGPU --overwrite=true"

# This was the old GPU exporter that was installed on the cluster.
# However, it was broken so has been removed.
- name: Uninstall GPU exporter
  kubernetes.core.k8s:
    state: absent
    definition: "{{lookup('template', item)}}"
    kubeconfig: "{{ kubeconfig_location }}"
  with_items:
    - gpu-exporter-118.yml
    - gpu-exporter-service.yml
    - gpu-exporter-service-monitor.yml
