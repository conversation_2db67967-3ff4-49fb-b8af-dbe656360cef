--- 
  - name: "Getting node-exporter binary file"
    get_url:
      url: "https://github.com/prometheus/node_exporter/releases/download/v1.0.1/node_exporter-1.0.1.linux-amd64.tar.gz"
      dest: /tmp/node-exporter.tar.gz


  - name: "Extracting binary file"
    unarchive: 
      src: "/tmp/node-exporter.tar.gz"
      dest: "/tmp"
      remote_src: yes

  - name: "Coping binary file"
    copy:
      src: /tmp/node_exporter-1.0.1.linux-amd64/node_exporter
      dest: /usr/local/bin/
      mode: 775
      remote_src: yes

  - name: Copying systemd service
    copy: 
      src: "node-exporter.service"
      dest: /etc/systemd/system

  - name: Enabling systemd service
    shell: "systemctl enable node-exporter.service"

  - name: Starting node-exporter service
    systemd: 
      name: "node-exporter"
      state: started