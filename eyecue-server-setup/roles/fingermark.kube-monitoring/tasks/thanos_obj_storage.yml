- name: Copying Thanos object storage configuration
  copy:
    src: thanos_bucket.yaml
    dest: /tmp/

- name: Looking for Thanos object storage secret
  shell: "{{ kubectl }} get secrets -n monitoring | grep thanos-objectstorage | awk '{ print $1 }'"
  register: thanos_secret


- name: Deleting existing Thanos object storage secret
  shell: "{{ kubectl }} delete secret thanos-objectstorage -n monitoring"
  when: thanos_secret.stdout != ""

- name: Creating Thanos secret
  shell: "{{ kubectl }} -n monitoring create secret generic thanos-objectstorage --from-file=bucket.yaml=/tmp/thanos_bucket.yaml"
  
- name: Removing Thanos object storage YAML file
  file:
    path: /tmp/thanos_bucket.yaml
    state: absent