---
- name: Deleting logging namespace
  shell: "{{ kubectl }} delete ns logging"
  when: delete_logging_ns
  ignore_errors: yes
  
- name: Copying fluent-bit files
  copy:
    src: fluent-bit
    dest: /tmp/

- name: Copying fluent-bit configmap
  template:
      src: fluent-bit-configmap.yaml
      dest: /tmp/fluent-bit/

- name: Copying fluent-bit daemon set
  template:
      src: fluent-bit-daemon-set.yaml
      dest: /tmp/fluent-bit/

- name: Creating logging namespace
  shell: "{{ kubectl }} create namespace logging"
  ignore_errors: true

- name: Creating fluent-bit resources
  shell: "{{ kubectl }} apply -f /tmp/fluent-bit"
