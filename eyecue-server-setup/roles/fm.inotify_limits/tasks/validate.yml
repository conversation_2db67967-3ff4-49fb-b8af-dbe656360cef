# ==============================================================================
# Validate the inotify_max_user_instances setting
# ==============================================================================

- name: Validate the inotify_max_user_instances setting
  shell: sysctl -n fs.inotify.max_user_instances
  register: inotify_actual_value
  changed_when: false

- name: Ensure inotify_max_user_instances is set to the correct value
  assert:
    that:
      - inotify_actual_value.stdout == "{{ inotify_max_user_instances }}"
    fail_msg: "inotify max_user_instances was not set correctly."

# =============================================================================
# Validate the inotify_max_user_watches setting
# ==============================================================================

- name: Validate the inotify_max_user_watches setting
  shell: sysctl -n fs.inotify.max_user_watches
  register: inotify_actual_value
  changed_when: false

- name: Ensure inotify_max_user_watches is set to the correct value
  assert:
    that:
      - inotify_actual_value.stdout == "{{ inotify_max_user_watches }}"
    fail_msg: "inotify max_user_watches was not set correctly."

# =============================================================================
# Validate the file_max setting
# ==============================================================================

- name: Validate the file_max setting
  shell: sysctl -n fs.file-max
  register: file_max_actual_value
  changed_when: false

- name: Ensure file_max is set to the correct value
  assert:
    that:
      - file_max_actual_value.stdout == "{{ file_max }}"
    fail_msg: "file_max was not set correctly."
