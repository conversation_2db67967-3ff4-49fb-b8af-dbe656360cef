- name: Set sysctl parameters with persistence
  ansible.posix.sysctl:
    name: "{{ item.name }}"
    value: "{{ item.value }}"
    sysctl_file: "{{ item.file }}"
    state: present
    reload: yes
  loop:
    - { name: 'fs.inotify.max_user_instances', value: '{{ inotify_max_user_instances }}', file: '/etc/sysctl.d/20-fs-inotify.conf' }
    - { name: 'fs.inotify.max_user_watches', value: '{{ inotify_max_user_watches }}', file: '/etc/sysctl.d/20-fs-inotify.conf' }
    - { name: 'fs.file-max', value: '{{ file_max }}', file: '/etc/sysctl.d/20-fs-file-max.conf' }
