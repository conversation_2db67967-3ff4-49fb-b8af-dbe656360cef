---
- name: Add Nvidia-Docker repository key
  ansible.builtin.apt_key:
    url: https://nvidia.github.io/nvidia-docker/gpgkey
    state: present

- name: Add Nvidia-Docker repository key
  ansible.builtin.apt_key:
    url: https://nvidia.github.io/nvidia-docker/gpgkey
    state: present

- name: Add Nvidia-Docker repository source
  ansible.builtin.get_url:
    url: https://nvidia.github.io/nvidia-docker/{{ansible_distribution|lower}}{{ansible_distribution_version}}/nvidia-docker.list
    dest: /etc/apt/sources.list.d/nvidia-docker.list
    owner: root
    group: root
    mode: 0644

# The default output of
# docker version --format '{{.Server.Version}}'
# is
# 18.06.1-ce
# We need to delete "-ce"
# later it is used like
# nvidia-docker2=2.0.3+docker18.06.1-1
- name: Get Docker version
  shell: "echo $(docker version --format '{{ '{{' }}.Server.Version{{ '}}' }}' | sed -e 's,-ce,,g')"
  register: command_output

- ansible.builtin.set_fact:
    docker_version: "{{ command_output.stdout }}"

- ansible.builtin.debug:
    msg: "Docker version {{ docker_version }}"

- name: Install nvidia-docker2
  ansible.builtin.apt:
    pkg: ['nvidia-docker2']
    update_cache: yes
    state: present
  register: nvidia_docker_apt
  notify: Restart Docker

- name: Ensure /etc/docker directory exists
  ansible.builtin.file:
    path: /etc/docker
    state: directory
    mode: '0755'

- name: Check if daemon.json exists
  ansible.builtin.stat:
    path: /etc/docker/daemon.json
  register: daemon_json_stat

- name: Create daemon.json with empty object if it doesn't exist
  ansible.builtin.copy:
    content: '{}'
    dest: /etc/docker/daemon.json
    mode: '0644'
  when: not daemon_json_stat.stat.exists

- name: Backup docker daemon.json
  ansible.builtin.command: cp /etc/docker/daemon.json /etc/docker/daemon.json-BACKUP
  when: daemon_json_stat.stat.exists

- name: Creating temporary daemon.json file
  ansible.builtin.file:
    path: /etc/docker/daemon.json.new
    state: touch
    mode: u=rw,g=r,o=r

- name: Adding daemon settings
  ansible.builtin.shell:
    cmd: 'jq ". + {\"default-runtime\": \"nvidia\", \"runtimes\": {\"nvidia\": {\"path\": \"/usr/bin/nvidia-container-runtime\", \"runtimeArgs\": []}}}" /etc/docker/daemon.json > /etc/docker/daemon.json.new'
  args:
    executable: /bin/bash

- name: Using new daemon.json file
  ansible.builtin.command: mv /etc/docker/daemon.json.new /etc/docker/daemon.json 

- name: Verify Docker Runtime
  ansible.builtin.command: "docker info --format '{% raw %}{{json .DefaultRuntime}}{% endraw %}'"
  register: docker_runtime

- ansible.builtin.debug:
    msg: "Docker runtime is {{ docker_runtime.stdout[1:-1] }}"

- name: Reload docker configuration
  ansible.builtin.service: 
    name: docker 
    state: reloaded
  when: (nvidia_docker_apt is changed) or (docker_runtime.stdout[1:-1] != "nvidia")

- name: Ignoring Nvidia repository (due to too frequent updates)
  ansible.builtin.command: mv /etc/apt/sources.list.d/nvidia-docker.list /etc/apt/sources.list.d/nvidia-docker.list.save

- name: Flush handlers
  meta: flush_handlers