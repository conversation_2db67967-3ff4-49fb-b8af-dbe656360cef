#!/bin/bash

if [ -z $1 ];
then
  echo -e "Please specify the server as arg 1"
  exit 1
fi

if [ -z $2 ]; 
then
  echo -e "Please specify the password as arg 2"
  exit 1
fi

SERVER=$1

PASSRD=$2

HASHED=$(/snap/bin/bcrypt-tool hash $PASSRD)
echo $HASHED

ssh $SERVER.eyeq.vpn "sudo kubectl --kubeconfig=/etc/kubernetes/admin.conf  -n argocd patch secret argocd-secret \
  -p '{\"stringData\": {
    \"admin.password\": \"$HASHED\",
    \"admin.passwordMtime\": \"'$(date +%FT%T%Z)'\"
  }}'"