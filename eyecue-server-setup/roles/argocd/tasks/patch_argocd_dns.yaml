---
- name: Set LABEL_SELECTOR environment variable
  ansible.builtin.set_fact:
    LABEL_SELECTOR: "app.kubernetes.io/part-of=argocd"

- name: Set IP_ADDRESS environment variable
  ansible.builtin.set_fact:
    IP_ADDRESS: "**************"

- name: Set HOSTNAME environment variable
  ansible.builtin.set_fact:
    HOSTNAME: "bitbucket.org"

- name: Update Kubernetes deployments with hostAliases
  shell: |
    export KUBECONFIG=/etc/kubernetes/admin.conf && \
    kubectl -n argocd get deployments -l {{ LABEL_SELECTOR }} -o json | \
    jq '.items[] | .spec.template.spec.hostAliases += [{"ip": "{{ IP_ADDRESS }}", "hostnames": ["{{ HOSTNAME }}"]}]' | \
    kubectl apply -f -

- name: "Logging into ArgoCD"
  ansible.builtin.shell: "argocd login 127.0.0.1:30080 --username admin --password {{ argocd_password }} --insecure"

- name: "Adding Bitbucket repository"
  shell: "argocd repo add {{ argocd_bitbucket_repo }} --ssh-private-key-path {{ dest_ssh_key }} --insecure-ignore-host-key --name {{ app_name }}"
  register: add_repo_dns
  retries: 5
  delay: 10
  until: add_repo_dns.rc == 0

- name: "Creating Eyecue application"
  shell: "argocd app create {{ app_name }} --repo={{ argocd_bitbucket_repo }} --path={{ ansible_hostname }} --dest-server https://kubernetes.default.svc --dest-namespace nmp-{{ ansible_hostname }} --release-name={{ app_name }} --values=values.yaml --upsert"

- name: Update DNS policy and configuration for deployments
  shell: |
    export KUBECONFIG=/etc/kubernetes/admin.conf && \
    kubectl get deployments -n nmp-{{ ansible_hostname }} -o json | \
    jq '.items[] | .spec.template.spec.dnsPolicy = "None" | .spec.template.spec.dnsConfig = {
      "nameservers": ["**********", "*******", "*******"],
      "options": [{"name": "ndots", "value": "5"}],
      "searches": ["eyeq.vpn", "nmp-{{ ansible_hostname }}.svc.cluster.local", "svc.cluster.local"]
    }' | kubectl apply -f -

- name: "Avoiding AutoSync"
  shell: "argocd app set eyecue --sync-policy none"