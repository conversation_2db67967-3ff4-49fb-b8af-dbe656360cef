---
- name: "Installing argocd-cli"
  ansible.builtin.get_url: 
    url: "https://github.com/argoproj/argo-cd/releases/download/{{ argocd_cli_version }}/argocd-linux-amd64"
    dest: /usr/local/bin/argocd
    mode: "0755"

- name: "Creating argocd namespace"
  kubernetes.core.k8s:
    api_version: v1
    kind: Namespace
    name: argocd
    state: present
    kubeconfig: "{{ kubeconfig_location }}"

###### TODO ######
# Istio setup
# - name: Set labels to eyecue namespace for istio
#   shell: "{{ kubectl }} label ns argocd istio-injection=enabled"
#   ignore_errors: yes

- name: "Creating kubernetes secrets for ArgoCD"
  kubernetes.core.k8s:
    state: present
    template: 
      - path: "./files/secrets.yml"
    namespace: "{{ argocd_ns}}"
    kubeconfig: "{{ kubeconfig_location }}"

- name: Downloading ArgoCD manifest
  ansible.builtin.get_url:
    url: "{{ argocd_manifests_url }}"
    dest: /tmp/argocd-install.yaml
    mode: '0664'

- name: Wait for the Calico Node pods to become ready
  kubernetes.core.k8s_info:
    kind: Deployment
    name: calico-kube-controllers
    wait: true
    kubeconfig: "{{ kubeconfig_location }}"
    namespace: kube-system
    wait_condition:
      type: Available

- name: "Creating argocd environment"
  kubernetes.core.k8s: 
    state: present
    src: /tmp/argocd-install.yaml
    namespace: "{{ argocd_ns}}"
    kubeconfig: "{{ kubeconfig_location }}"

- name: "Patching ArgoCD service"
  kubernetes.core.k8s:
    kind: Service
    name: argocd-server
    namespace: "{{ argocd_ns}}"
    state: "patched"
    merge_type: "strategic-merge"
    definition:
      spec:
        type: "NodePort"
        ports:
          - nodePort: 30080
            port: 80
          - nodePort: 32691
            port: 443
    kubeconfig: "{{ kubeconfig_location }}"

- name: Patching ArgoCD Password
  kubernetes.core.k8s:
    kind: Secret
    name: argocd-secret
    namespace: "{{ argocd_ns}}"
    state: "patched"
    merge_type: "strategic-merge"
    definition:
      stringData: 
        admin.password: "$2a$10$v/bavty7dNhJOnPyXjG7teodoJJ9N21sTzs4xFglMPj4KDol7snj6"
    kubeconfig: "{{ kubeconfig_location }}"