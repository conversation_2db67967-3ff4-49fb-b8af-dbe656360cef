---
- name: "Creating ArgoCD S3 proxy"
  kubernetes.core.k8s:
    state: present
    template: 
      - path: "./templates/argocd-proxy.tpl.yml"
    namespace: "{{ argocd_ns}}"
    kubeconfig: "{{ kubeconfig_location }}"

- name: "Setup SSH"
  ansible.builtin.include_tasks: install_ssh_key.yaml

- name: Creating Eyecue namespace
  kubernetes.core.k8s:
    api_version: v1
    kind: Namespace
    name: nmp-{{ ansible_hostname }}
    state: present
    kubeconfig: "{{ kubeconfig_location }}"

- name: Checking if repository exists
  shell: argocd repo list | grep eyecue | awk '{print $2}'
  register: argocd_repo

- name: "Adding Bitbucket repository"
  shell: "argocd repo add {{ argocd_bitbucket_repo }} --ssh-private-key-path {{ dest_ssh_key }} --insecure-ignore-host-key --name {{ app_name }}"
  register: add_repo_deploy
  retries: 5
  delay: 10
  until: add_repo_deploy.rc == 0
  when: argocd_repo.stdout != 'eyecue'

- name: "Creating Eyecue application"
  shell: "argocd app create {{ app_name }} --repo={{ argocd_bitbucket_repo }} --path={{ ansible_hostname }} --dest-server https://kubernetes.default.svc --dest-namespace nmp-{{ ansible_hostname }} --release-name={{ app_name }} --values=values.yaml"
