---
- name: Installing dependencies
  ansible.builtin.apt:
    name: curl
    state: present

- name: "Deleting ArgoCD namespace"
  kubernetes.core.k8s:
    api_version: v1
    kind: Namespace
    name: argocd
    state: absent
    kubeconfig: "{{ kubeconfig_location }}"
    wait: yes
  when: remove_argocd_ns

- name: "ArgoCD setup"
  ansible.builtin.include_tasks: 
    file: argocd_setup.yaml
  
- name: Waiting for ArgoCD nodeport to be available 
  ansible.builtin.wait_for:
    port: "30080"
    delay: 10

- name: "Logging into ArgoCD"
  ansible.builtin.shell: "argocd login 127.0.0.1:30080 --username admin --password {{ argocd_password }} --insecure"

- name: Checking if application is already deployed
  ansible.builtin.shell: argocd app list | grep {{ app_name }} | awk '{print $1}'
  register: argocd_app
  
- ansible.builtin.include_tasks: eyecue_deployment.yml
  when: argocd_app.stdout != app_name

- ansible.builtin.include_tasks: sync_windows.yml
  when: argocd_enable_windows_sync

- name: "Sync the ArgoCD application"
  ansible.builtin.include_tasks: 
    file: run_sync.yml
  when: argocd_app_sync 