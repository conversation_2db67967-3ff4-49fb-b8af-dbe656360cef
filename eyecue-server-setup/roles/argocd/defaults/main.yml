---
dest_ssh_key: /home/<USER>/.ssh/bitbucket.id_rsa
kubeconfig_location: "/etc/kubernetes/admin.conf"
helm_bucket_name: eyecue-helm-cv-prod-package
helm_bucket_default_region: ap-southeast-2
app_name: eyecue
argocd_cli_version: "v2.1.9"
argocd_manifests_url: "https://raw.githubusercontent.com/argoproj/argo-cd/{{ argocd_cli_version }}/manifests/install.yaml"
argocd_bitbucket_repo: "*****************:fingermarkltd/eyecue-mcdonalds-helm"
argocd_ns: "argocd"
argocd_bin_update: false
argocd_first_install: true
argocd_enable_windows_sync: true
argocd_app_sync: false
argocd_password: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  38393234633630343536633433643764643432353365333362376236323437333263323161646362
  3565373536363061643764393333366666373561653435310a363432323333633833343831363564
  32336335383732383134356361663937326239323438643363626435656136666339626134623733
  3161356432613434370a356530653237343165633637303230636139313339326638616664383231
  3337
