---
- name: "Adding CUSTOMER env var"
  ansible.builtin.lineinfile:
   line: CUSTOMER={{ customer }}
   path: /etc/environment

- name: "Create SSH folder if missing"
  ansible.builtin.file:
    path: /root/.ssh/
    state: directory

- name: "Create SSH config file if missing"
  ansible.builtin.file:
    path: /root/.ssh/config
    state: touch

- name: "Create SSH config file if missing"
  ansible.builtin.file:
    path: /etc/cron.d/kommisjon
    state: touch
    mode: "0644"

- name: "Create ssh_config"
  ansible.builtin.lineinfile:
    path: /root/.ssh/config
    line: |
      Host *
      StrictHostKeyChecking no
    state: present

- name: Create kommisjon directory
  ansible.builtin.file:
    path: "{{ kommisjon_app_dir }}"
    state: "directory"

- name: Copying SSH private file for cloning the Kommisjon bootstrap project
  ansible.builtin.copy:
    src: id_rsa
    dest: /root/.ssh/id_rsa
    mode: 0400

- name: "Clone Bitbucket repository locally"
  ansible.builtin.git:
    repo: "{{ kommisjon_git_repo_addr }}"
    version: "master"
    dest: "{{ kommisjon_git_repo_dir }}"
    accept_hostkey: true
    key_file: /root/.ssh/id_rsa

- name: "Copying 00-header file"
  ansible.builtin.copy:
    src: 00-header
    dest: /etc/update-motd.d/00-header
    mode: 0755

- name: "Copy config.ini"
  ansible.builtin.copy:
    src: "{{ kommisjon_config_ini_path }}"
    dest: "{{ kommisjon_app_dir }}"
  when: inventory_hostname != 'localhost'

