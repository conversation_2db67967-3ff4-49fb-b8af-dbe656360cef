server {
    listen 80;
    server_name localhost;
    root /var/www/html;

    location ~ /(.*).eyeq.vpn/argus/(.*) {
        # Catch all incase there's a request method we're not dealing with properly
        # add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Origin' "*";
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
        add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range';

        resolver ********** [::1];
        proxy_pass http://$1.eyeq.vpn:9876/$2?$query_string;

    }

    # location ~ /host/(.*)/argument/(.*) {
    location ~ /(.*).eyeq.vpn/(.*) {
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
        add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range';

        resolver ********** [::1];
        proxy_pass http://$1.eyeq.vpn:7333/$2?$query_string;
    }
}
# ([^/])*/(.*)
