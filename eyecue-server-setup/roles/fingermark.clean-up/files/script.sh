kubectl --kubeconfig /etc/kubernetes/admin.conf delete deploy -n infra minio-deployment-infra
sleep 10
rm -rf /media/fingermark/storage/minio-*/

sudo systemctl stop kubelet
sudo systemctl stop docker

sleep 10

sudo systemctl start docker
sudo systemctl start kubelet 

while ! nc -z localhost 6443; do sleep 1;echo "Waiting for k8s to be available..."; done

kubectl --kubeconfig /etc/kubernetes/admin.conf delete deploy -n infra grafana
kubectl --kubeconfig /etc/kubernetes/admin.conf delete deploy -n infra mongo
kubectl --kubeconfig /etc/kubernetes/admin.conf delete deploy -n infra victoria-metrics
kubectl --kubeconfig /etc/kubernetes/admin.conf delete deploy -n infra victoria-metrics-agent
kubectl --kubeconfig /etc/kubernetes/admin.conf delete deploy -n infra minio-deployment-infra

kubectl --kubeconfig /etc/kubernetes/admin.conf patch pvc -n infra grafana-pvc-infra -p '{"metadata":{"finalizers":null}}'
kubectl --kubeconfig /etc/kubernetes/admin.conf patch pvc -n infra minio-pv-claim-infra -p '{"metadata":{"finalizers":null}}'
kubectl --kubeconfig /etc/kubernetes/admin.conf patch pvc -n infra mongo-data-infra -p '{"metadata":{"finalizers":null}}'
kubectl --kubeconfig /etc/kubernetes/admin.conf patch pvc -n infra victoria-metrics-agent-pvc-infra -p '{"metadata":{"finalizers":null}}'
kubectl --kubeconfig /etc/kubernetes/admin.conf patch pvc -n infra victoria-metrics-pvc-infra -p '{"metadata":{"finalizers":null}}'

kubectl --kubeconfig /etc/kubernetes/admin.conf delete pvc -n infra --all
kubectl --kubeconfig /etc/kubernetes/admin.conf delete pv --all -n infra 
while ! nc -z localhost 30080; do sleep 1;echo "Waiting for argo to be available..."; done
argocd login 127.0.0.1:30080 --username admin --password q1w2e3r4 --insecure
while argocd app terminate-op eyecue; do echo "Stopping any running sync jobs"; sleep 10; done && echo "Sync stopped"
argocd app sync eyecue --prune --force