all:
  vars:
    ansible_python_interpreter: /usr/bin/python3
    fingermark_product: eyecue
    customer: hrd 
    kubeadmin_config: "/etc/kubernetes/admin.conf"    
  children:
    infrastructure:
      children:
        icinga2_satellite:
          hosts: 
            icinga2-satellite.amr.infra.fingermark.tech
          vars:
            softether_client_private_ip: **********
            icinga2_satellite_endpoint: api.icinga2-master.infra.fingermark.tech
    production:
      vars:        
        pod_network_cidr: "**********/16"        
        icinga2_satellite_endpoint: "icinga2-satellite.amr.infra.fingermark.tech"
        meshid: "XbnehF9XD7XYjxHy"
        argocd_bitbucket_repo: "*****************:fingermarkltd/eyecue-amr-helm"
      children:                
        # nshama_server: (MOVED TO POC)
        #   hosts:
        #       fm-hrd-uae-001.eyeq.vpn
        #   vars:
        #     coordinates: "25.010543,55.289622"
        #     display_name: "<PERSON><PERSON><PERSON>"
        #     timezone:
