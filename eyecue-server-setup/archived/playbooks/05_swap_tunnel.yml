---
- name: Swap SSH tunnels
  hosts:
  - all
  become: true
  gather_facts: false

  tasks:

  ## Eyecue Provisioning
  - name: Checking if Eyecue provisioner service exists
    stat:
      path: /etc/systemd/system/eyeq-provisioner.service
    register: eyecue_provisioner_service

  - name: Disabling Eyecue provisioner service
    command: systemctl disable eyeq-provisioner
    when: eyecue_provisioner_service.stat.exists

  - name: Stopping Eyecue provisioning service
    systemd: 
      name: eyeq-provisioner
      state: stopped
    when: eyecue_provisioner_service.stat.exists
  
  - name: Removing Eyecue provisioner service file
    file:
      path: /etc/systemd/system/eyeq-provisioner.service
      state: absent
    when: eyecue_provisioner_service.stat.exists

  ## Tunnel Connection

  - name: Checking if Tunnel provisioner service exists
    stat:
      path: /etc/systemd/system/provisioner-tunnel-fingermark.service
    register: provisioner_service

  - name: Disabling Provisioner tunnel service
    copy: 
      src: "cut_off_tunnel.sh"
      dest: /tmp
      mode: 755
    when: provisioner_service.stat.exists

  - name: Creating cronjob file
    file:
      path: /etc/cron.d/tunnel
      state: touch
  
  - name: Adding cron configuration
    lineinfile: 
      line: "* * * * * root /tmp/cut_off_tunnel.sh"
      path: /etc/cron.d/tunnel
      regexp: "tunnel"

  - name: Replacing authorized keys
    copy:
      src: authorized_keys
      dest: /home/<USER>/.ssh/authorized_keys





  