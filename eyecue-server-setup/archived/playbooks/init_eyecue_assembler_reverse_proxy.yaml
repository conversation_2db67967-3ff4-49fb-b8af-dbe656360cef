---
- name: Initialize assembler_reverse_proxy server
  become: true
  hosts: eyecue_proxy

  vars:
  - softether_virtualhub: servers
  - softether_client_username: eyecue_proxy
  - vpn_user_password: # in 1 pass
  - docker_version: "5:19.03.10~3-0~ubuntu-bionic"
  - softether_client_private_ip: ************** #192.168.100.## don't forget to add to ip ranges/monitoring deployments 

  roles:
  # - fm.users
  - fm.softether
  - { role: 'geerlingguy.docker', tags: 'docker' }
  - fm.eyecue-nginx-proxy

  tasks:
  - name: Change hostname
    hostname:
      name: "eyecue_proxy"

  - lineinfile:
      path: /etc/hosts
      line: "*********\teyecue_proxy" 
      regexp: "^*********"

  - name: Setting up ssh client
    lineinfile:
      path: /etc/ssh/ssh_config
      line: StrictHostKeyChecking no
      regexp: StrictHostKeyChecking    
