---
- name: Initialize deployer server
  become: true
  hosts: deployer

  vars:
  - onepass_cli_url: https://cache.agilebits.com/dist/1P/op/pkg/v1.3.0
  - onepass_cli_file: op_linux_amd64_v1.3.0.zip
  - repository_path: /usr/share/eyecue-server-setup
  - softether_virtualhub:  servers
  - softether_client_username: deployer
  - vpn_user_password: 

  roles:
  - fm.users
  - fm.softether

  tasks:
  - name: Installing dependencies
    apt:
      name: 
      - unzip
      - sshpass
      state: present


  - name: Change hostname
    hostname:
      name: "deployer"

  - lineinfile:
      path: /etc/hosts
      line: "*********\tdeployer" 
      regexp: "^*********"


  - name: Setting up ssh client
    lineinfile:
      path: /etc/ssh/ssh_config
      line: StrictHostKeyChecking no
      regexp: StrictHostKeyChecking
  
  

  - name: Cloning repository
    git:
      repo: *****************:fingermarkltd/eyecue-server-setup.git
      dest: "{{ repository_path }}"
      key_file: deployer.id_rsa      
  
  - name: Seting up repository permissions
    file: 
      path: "{{ repository_path }}"
      owner: root
      group: deployers
      mode: 0774
      recurse: true

  - name: Adding command to remove inventory on every logout
    lineinfile:
      path: /home/<USER>/.bash_logout
      line: rm ~/.ansible/hosts ~/.ssh/fingermark.id_rsa
      regexp: ansible/hosts
    with_items: 
    - "{{users.admin}}"
    - "{{users.developer}}"

  - name: Copying the fingermark.id_rsa when the users logs in
    lineinfile:
      path: /home/<USER>/.bashrc 
      line: "cp {{ repository_path }}/fingermark.id_rsa ~/.ssh/"
      regexp: fingermark.id_rsa
    with_items: 
    - "{{ users.admin }}"
    - "{{ users.developer }}"      

  - name: Setting ansible-playbook alias
    lineinfile:
      path: /home/<USER>/.bashrc
      line: "alias ansible-playbook='ansible-vault decrypt ~/.ssh/fingermark.id_rsa; ansible-playbook -i ~/.ansible/hosts'"
      regexp: ansible/hosts
    with_items: 
    - "{{ users.admin }}"
    - "{{ users.developer }}"

  - name: Adding users to deployers groups
    user:
      name: "{{ item }}"
      group: deployers
      append: true
    with_items:
    - "{{ users.admin }}"
    - "{{ users.developer }}"

  - name: Creating ansible folder for each user
    file:
      path: "/home/<USER>/.ansible"
      owner: "{{ item }}"
      group: "{{ item }}"
      state: directory
    with_items: 
    - "{{ users.admin }}"
    - "{{ users.developer }}"  

  - name: Installing 1Password CLI tool
    get_url:
      url: "{{ onepass_cli_url }}/{{ onepass_cli_file }}"
      dest: /tmp/      

  - name: Decompressing 1Password CLI
    unarchive:
      src: "/tmp/{{ onepass_cli_file }}"
      dest: /usr/local/bin
      remote_src: true
