---
- name: <PERSON>germark Computer Vision Server
  hosts: "{{ run_hosts | split(',') | default('all')}}"

  become: true

  pre_tasks:
    - name: "Changing hostname"
      ansible.builtin.hostname:
        name: "{{ hostname | default(ansible_hostname) }}"
      tags:
        - "basics"
        - "hostname"
    - name: "Updating hosts file"
      ansible.builtin.lineinfile:
        path: /etc/hosts
        line: "*********\t{{ hostname | default(ansible_hostname) }}\t{{ hostname | default(ansible_hostname) }}.eyeq.vpn"
        regexp: "^*********"
      tags:
        - "basics"
        - "hostname"

    - name: Setting DISPLAY_NAME env var
      ansible.builtin.lineinfile:
        line: DISPLAY_NAME="{{ display_name }}"
        path: /etc/environment
        regexp: DISPLAY_NAME
      tags:
        - "basics"
        - "hostname"

  roles:
    - role: "fm.users"
      tags:
        - "users"
        - "basics"
        - "access"
    - role: "fm.teleport"
      vars:
        create_teleport_cluster: true
      tags:
        - "access"
        - "teleport"
    - role: "fingermark.nvidia-driver"
      vars:
        nvidia_driver_version: 515
        nvidia_driver_use_ppa: ppa:graphics-drivers/ppa
      tags:
        - "drivers"
        - "nvidia"
        - "basics"
    - role: "geerlingguy.docker"
      vars:
        docker_version: "5:20.10.14~3-0~ubuntu-{{ ansible_distribution_release | lower }}"
      tags:
        - "docker"
    - role: "fingermark.nvidia-docker"
      tags:
        - "nvidia"
        - "docker"
    - role: "fm.mount_drive"
      tags:
        - "basics"
        - "storage"
    - role: "clamav"
      tags:
        - "security"
