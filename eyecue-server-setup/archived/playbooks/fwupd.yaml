---
- name: Install fwupd with Snap
  become: yes
  gather_facts: false
  hosts: 
  - morayfield_east

  tasks:
  - name: <PERSON><PERSON>ve fwupd from apt pkg manager
    apt:
      name: fwupd
      state: absent
      
  - name: Install Snap pkg manager
    apt:
      name: snapd
      state: present

  - name: Install fwupd with Snap
    snap:
      name: fwupd
      classic: true
      state: present

  - name: Update path 
    lineinfile: 
      line: 'export PATH=$PATH:/snap/bin'
      path: /root/.bashrc
      regexp: /snap/bin$
