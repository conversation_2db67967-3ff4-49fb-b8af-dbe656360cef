all:
  vars:
    ansible_python_interpreter: /usr/bin/python3
  children:
    production:
      vars:        
        pod_network_cidr: "**********/16"
        kubeadmin_config: "/etc/kubernetes/admin.conf"
        meshid: ""
      children:                
        bugcrowd_001:
          hosts:
              *************
          vars:
            coordinates: "-176.879394,-39.667736"
            display_name: "Bugcrowd 001"
            timezone: "Australia/Sydney"

        bugcrowd_helm_parser:
          hosts:
            ************
          vars:
            ansible_user: ubuntu
            ansible_ssh_private_key_file: ~/Documents/keys/aws-bugcrowd/matias.pem
        bugcrowd_vpn:
          hosts:
            **************
          vars:
            ansible_user: ubuntu
            ansible_ssh_private_key_file: ~/Documents/keys/aws-bugcrowd/matias.pem
        bugcrowd_autoprovisioner:
          hosts:
            ***********
          vars:
            ansible_user: ubuntu
            ansible_ssh_private_key_file: ~/Documents/keys/aws-bugcrowd/matias.pem
        jump_host:
          hosts:
            *************
          vars:
            ansible_user: ubuntu
            ansible_ssh_private_key_file: ~/Documents/keys/aws-bugcrowd/matias.pem