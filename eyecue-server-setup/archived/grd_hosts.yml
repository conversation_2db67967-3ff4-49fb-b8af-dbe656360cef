all:
  vars:
    ansible_python_interpreter: /usr/bin/python3
    fingermark_product: eyecue
    customer: grd
    kubeadmin_config: "/etc/kubernetes/admin.conf"
  children:
    infrastructure:
      children:
        icinga2_satellite:
          hosts: 
            icinga2-satellite.grd.infra.fingermark.tech
          vars:
            softether_client_private_ip: **********
            icinga2_satellite_endpoint: api.icinga2-master.infra.fingermark.tech
    production:
      vars:        
        pod_network_cidr: "**********/16"
        aws_s3_bucket_camera_images: eyecue-grd-us-camera-images
        meshid: "lSarU6Gy3NgXt4nH"
        argocd_bitbucket_repo:
      children:
        head_office:
          hosts:
              fm-grd-aus-0001.eyeq.vpn
          vars:
            coordinates: "-34.92111,138.63661"
            display_name: "Head Office"
            timezone: "Australia/Adelaide"