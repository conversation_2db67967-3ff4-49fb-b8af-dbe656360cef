---
- name: Create disk partition for Moonfire
  become: true
  gather_facts: true
  hosts: localhost
  vars:
    disk_to_partition: /dev/sda

    partitions:
      - mount_path: /media/fingermark/storage
        part_start: 0%
        part_end: 20%
      - mount_path: /media/fingermark/storage/moonfire-nvr
        part_start: 20%
        part_end: 100%

    backup:
      - mongodb-infra

  tasks:
    - name: Create temp directory
      ansible.builtin.tempfile:
        state: directory
      register: temp_dir
      when: backup | length > 0

    - name: Backup existing data
      ansible.builtin.copy:
        remote_src: true
        src: "{{ ['/media/fingermark/storage', item] | path_join }}"
        dest: "{{ temp_dir.path }}"
        mode: preserve
      loop: "{{ backup }}"
      when: backup | length > 0

    - name: Create directories for partitions
      ansible.builtin.file:
        path: "{{ item.mount_path }}"
        state: directory
        mode: "0755"
      loop: "{{ partitions }}"

    - name: Unmount existing partitions
      ansible.posix.mount:
        path: "{{ item.mount_path }}"
        state: unmounted
      loop: "{{ partitions | reverse }}"  # reverse so it can unmount the innermost first
      failed_when: false
      changed_when: false

    - name: Create partitions
      community.general.parted:
        device: "{{ disk_to_partition }}"
        number: "{{ index + 1 }}"
        part_start: "{{ item.part_start }}"
        part_end: "{{ item.part_end }}"
        state: present
      loop: "{{ partitions }}"
      loop_control:
        index_var: index

    - name: Format partitions
      community.general.filesystem:
        dev: "{{ disk_to_partition }}{{ index + 1 }}"
        fstype: ext4
      loop: "{{ partitions }}"
      loop_control:
        index_var: index

    - name: Mount partitions
      ansible.posix.mount:
        path: "{{ item.mount_path }}"
        src: "{{ disk_to_partition }}{{ index + 1 }}"
        fstype: ext4
        state: mounted
      loop: "{{ partitions }}"
      loop_control:
        index_var: index
      changed_when: false

    - name: Restore data
      ansible.builtin.copy:
        remote_src: true
        src: "{{ [temp_dir.path, item] | path_join }}"
        dest: "/media/fingermark/storage/"
        mode: preserve
      loop: "{{ backup }}"
      when: backup | length > 0

    - name: Remove temp directory
      ansible.builtin.file:
        path: "{{ temp_dir.path }}"
        state: absent
      when: backup | length > 0
