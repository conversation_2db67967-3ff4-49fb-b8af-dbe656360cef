---
- name: Server Health Check
  hosts: "{{ run_hosts.split(',') }}"
  gather_facts: true
  become: true

  vars:
    kubeconfig_location: /etc/kubernetes/admin.conf
    icinga2_master_ip: **************

  tasks:
    - name: Check if the server is online
      ansible.builtin.set_fact:
        server_online: true

    - name: Check Secure Boot status
      ansible.builtin.shell: mokutil --sb-state
      register: secure_boot_result
      ignore_errors: true

    - name: Check Teleport service status
      ansible.builtin.systemd:
        name: teleport
        state: started
      register: teleport_output
      ignore_errors: true

    - name: Check VPN connection
      ansible.builtin.command: ping -c 2 {{ icinga2_master_ip }}
      register: vpn_result
      ignore_errors: true

    - name: Check Nvidia driver status
      ansible.builtin.command: nvidia-smi
      register: nvidia_smi_output
      ignore_errors: true

    - name: Check Kubernetes cluster version
      kubernetes.core.k8s_cluster_info:
        kubeconfig: "{{ kubeconfig_location }}"
      register: k8s_status
      ignore_errors: true

    - name: Check kubelet status
      ansible.builtin.command: systemctl is-active kubelet
      register: kubelet_status
      ignore_errors: true

    - name: Check Icinga2 service status
      ansible.builtin.systemd:
        name: icinga2
        state: started
      register: icinga2_output
      ignore_errors: true

    - name: Gather pod information
      kubernetes.core.k8s_info:
        kubeconfig: "{{ kubeconfig_location }}"
        kind: Pod
      register: pod_info
      ignore_errors: true

    - name: Set facts for output
      ansible.builtin.set_fact:
        secure_boot_status: "{{ secure_boot_result.stdout | default('SecureBoot state unknown') }}"
        teleport_status: "{{ teleport_output.status.ActiveState | default('Teleport is inactive') }}"
        vpn_status: "{{ 'The VPN is working well' if not vpn_result.failed else 'The VPN is down' }}"
        nvidia_driver_status: "{{ 'Nvidia driver is operational' if not nvidia_smi_output.failed else 'Nvidia driver is not operational' }}"
        k8s_version: "{{ k8s_status.version.server.kubernetes.gitVersion | default('Kubernetes status unknown') }}"
        icinga2_status: "{{ icinga2_output.status.ActiveState | default('Icinga2 is inactive') }}"
        nic_name: "{{ ansible_default_ipv4.alias | default('unknown') }}"
        ip_address: "{{ ansible_default_ipv4.address | default('unknown') }}/{{ ansible_default_ipv4.netmask | default('unknown') }}"
        gateway: "{{ ansible_default_ipv4.gateway | default('unknown') }}"

    - name: Construct all Eyecue pod information as list
      ansible.builtin.set_fact:
        eyecue_pods_list: |
          [
          {% for pod in pod_info.resources %}
          "Namespace: {{ pod.metadata.namespace }}    Status: {{ pod.status.phase }}    Name: {{ pod.metadata.name }}",
          {% endfor %}
          {% if pod_info.resources | length == 0 %}
          "No relevant Eyecue Server pods found"
          {% endif %}
          ]

    - name: Construct Triton Pod information as list
      ansible.builtin.set_fact:
        triton_pods_list: |
          [
          {% for pod in pod_info.resources if pod.metadata.name.startswith('triton') and pod.metadata.namespace == 'infra' %}
          "Pod Name: {{ pod.metadata.name }}, Status: {{ pod.status.phase }}",
          {% endfor %}
          {% if pod_info.resources | length == 0 %}
          "No relevant Triton Pods found"
          {% endif %}
          ]

    - name: Construct Tracker Pod information as list
      ansible.builtin.set_fact:
        tracker_pods_list: |
          [
          {% for pod in pod_info.resources if pod.metadata.name.startswith('eyeq-tracker') %}
          "Pod Name: {{ pod.metadata.name }}, Status: {{ pod.status.phase }}",
          {% endfor %}
          {% if pod_info.resources | length == 0 %}
          "No relevant Tracker Pods found"
          {% endif %}
          ]

    - name: Construct Eyecue Server Pod information as list
      ansible.builtin.set_fact:
        eyecue_server_pods_list: |
          [
          {% for pod in pod_info.resources if pod.metadata.name.startswith('eyeq-server') %}
          "Pod Name: {{ pod.metadata.name }}, Status: {{ pod.status.phase }}",
          {% endfor %}
          {% if pod_info.resources | length == 0 %}
          "No relevant Eyecue Server pods found"
          {% endif %}
          ]

    - name: Construct Argus Pod information as list
      ansible.builtin.set_fact:
        argus_pods_list: |
          [
            {%
              for pod in pod_info.resources
              if pod.metadata.labels.app is defined and pod.metadata.labels.app == 'eyecue-argus'
            %}
            "Pod Name: {{ pod.metadata.name }}, Status: {{ pod.status.phase }}",
            {% endfor %}
            {% if pod_info.resources | length == 0 %}
            "No relevant Argus pods found"
            {% endif %}
          ]

    - name: All Eyecue Pod information
      ansible.builtin.debug:
        msg: "{{ eyecue_pods_list | from_yaml }}"

    - name: Triton Pod information
      ansible.builtin.debug:
        msg: "{{ triton_pods_list | from_yaml }}"

    - name: Tracker Pod information
      ansible.builtin.debug:
        msg: "{{ tracker_pods_list | from_yaml }}"

    - name: Eyecue Server Pod information
      ansible.builtin.debug:
        msg: "{{ eyecue_server_pods_list | from_yaml }}"

    - name: Argus Pod information
      ansible.builtin.debug:
        msg: "{{ argus_pods_list | from_yaml }}"

    - name: Server health information
      ansible.builtin.debug:
        msg:
          - "Server Online: {{ server_online }}"
          - "Secure Boot Status: {{ secure_boot_status }}"
          - "Teleport Status: {{ teleport_status }}"
          - "VPN Status: {{ vpn_status }}"
          - "Nvidia Driver Status: {{ nvidia_driver_status }}"
          - "Kubernetes Cluster Version: {{ k8s_version }}"
          - "Icinga2 Status: {{ icinga2_status }}"
          - "Network Status: NIC Name: {{ nic_name }}, IP Address: {{ ip_address }}, Gateway: {{ gateway }}"
- import_playbook: timezone.yaml
