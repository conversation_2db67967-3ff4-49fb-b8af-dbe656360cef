---
- name: Check and restart Kubernetes and Docker services
  hosts: "{{ run_hosts.split(',') }}"
  become: true
  gather_facts: true
  vars:
    kube_config_location: "/etc/kubernetes/admin.conf"
  tasks:
    - name: Get the value of server from /etc/kubernetes/admin.conf
      shell: cat {{ kube_config_location }}
      register: admin_conf_output

    - name: Extract IP address from admin.conf server value
      set_fact:
        admin_conf_ip: "{{ admin_conf_output.stdout_lines | regex_search('server: https://(\\d+\\.\\d+\\.\\d+\\.\\d+):(\\d+)', '\\1') | first }}"

    - name: Display Value of Server from admin.conf
      debug:
        var: admin_conf_ip

    - name: Compare IP address with *************
      block:
        - name: Output message and restart services if IP matches
          debug:
            msg: "IP addresses match. Restarting Kubernetes and Docker services."
          when: admin_conf_ip == "*************"

        - name: Restart Kubernetes services
          service:
            name: kubelet
            state: restarted
        
        - name: Wait for kubectl to be available
          wait_for:
            timeout: 60
            delay: 5
            port: 6443
            state: started

        - name: Check kubectl is working
          command: kubectl get pods -A -o json
          environment:
            KUBECONFIG: "{{ kube_config_location }}"
          register: pods_output

        - name: Get the status of kubectl command
          debug:
            var: pods_output.rc
        
        - name: Restart Docker service if kubectl returns an error
          when: pods_output.rc != 0
          service:
            name: docker
            state: restarted

    - name: Output message if IP addresses do not match
      when: admin_conf_ip != "*************"
      debug:
        msg: "Kubeconfig IP does not match *************. Please contact the Infra team."
