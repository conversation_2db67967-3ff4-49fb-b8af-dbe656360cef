---
- name: <PERSON><PERSON><PERSON><PERSON>b check
  become: true
  gather_facts: true

  hosts:
    - production
  tasks:
    # - ansible.builtin.stat:
    #     path: /etc/cron.d/netplan-watchdog
    #   register: file

    # - ansible.builtin.debug:
    #     msg: "{{ ansible_product_serial }}"

    - command: echo $DISPLAY_NAME
      register: displayname
    
    - local_action:
        module: lineinfile
        line: "{{ displayname.stdout }}: {{ ansible_hostname }}: {{ ansible_product_serial }}"
        regexp: "{{ ansible_hostname }}"
        dest: /tmp/eyecue-servers-serial.txt
      # when: file.stat.exists