---
- name: "Setup Icinga2 Satellite"
  become: true
  hosts: icinga2_satellite # Instance created by Terraform
  vars:
    hostname: icinga2-satellite.{{ customer }}.infra.fingermark.tech
    fingermark_product: "eyecue"

  pre_tasks:
  - name: Changing hostname
    hostname:
      name: "{{ hostname }}"
    tags:
      - basics
      - hostname

  - lineinfile:
      path: /etc/hosts
      line: "*********\t{{ hostname }}\t{{ hostname }}"
      regexp: "^*********"
    tags:
      - basics
      - hostname


  roles:
    - role: fm.users
      tags:
        - basics
        - users

    - role: fm.softether
      vars:
        softether_user_realname: "Icinga2 Satellite"
        softether_virtualhub: "{{ customer }}"
        softether_client_username: "icinga2-satellite"
        vpn_user_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          34393637356363323538323230643831626164346538303062386262333864663033306537333366
          3238393530623162346332326436333133333234323865330a383964363232616361643865376566
          37626665393531653663303631623430343434633035323133613037653739346234656337353833
          3333653936333662310a343366623837333035333434663639386661636664366131363563393031
          3666
      tags:
        - access
        - vpn

    - role: 'geerlingguy.security'
      tags: 'security'

    - role: 'fm.icinga2'
      vars:
        icinga2_satellite_endpoint: api.icinga2-master.infra.fingermark.tech
        icinga2_master_endpoint: api.icinga2-master.infra.fingermark.tech
        icinga2_satellite_setup: true
        icinga2_node_setup: false
        icinga_force_setup: false
        icinga2_master_setup: false
        icinga2_master_node: "icinga2-master.infra.fingermark.tech"
        icinga2_install_db: false
      tags:
        - "monitoring"
        - "icinga2"
