---

- name: "Renew Kubeadm cluster certs"
  hosts: "{{ run_hosts | split(',') | default('localhost')}}"
  gather_facts: true
  become: true 
  vars:
    force_cert_renewal: false
    kubeadmin_config: /etc/kubernetes/admin.conf
    cert_type: all
  
  vars_prompt:
    - name: continue
      prompt: You are about to renew the certificate of the kubeadm cluster cluster. Do you want to continue? (y/N)
      private: false
  pre_tasks:
    - name: "Installing dependencies"
      ansible.builtin.apt:
        name: python3-pip
    - name: Updating system PATH
      ansible.builtin.lineinfile:
        line: 'export PATH="$PATH:/snap/bin"'
        regexp: /snap/bin
        path: /root/.bashrc
    - name: install kubernetes python package
      ansible.builtin.pip: 
        name: kubernetes

  tasks:
    - fail:
      when: "continue != 'y'"


    - name: Get Cluster information
      kubernetes.core.k8s_cluster_info:
        kubeconfig: "{{ kubeadmin_config }}"
      register: api_status
      ignore_errors: True

    - name: Show cluster status
      debug:
        msg: "{{ api_status }}"


    - name: Check Kubeadm certs
      shell: "kubeadm alpha certs check-expiration"
      register: cert_expiration

    - name: Kubeadm Cert Output
      debug:
        msg: "{{ cert_expiration.stdout }}"


    - name: Renew Kubeadm certs
      shell: "kubeadm alpha certs renew {{ cert_type }}"
      when: ('invalid' in cert_expiration.stdout) or (force_cert_renewal)

    - name: Recheck Kubeadm certs
      shell: "kubeadm alpha certs check-expiration"
      register: recheck_cert_expiration
      when: ('invalid' in cert_expiration.stdout) or (force_cert_renewal)


    - name: Kubeadm Cert Output
      debug:
        msg: "{{ recheck_cert_expiration.stdout }}"
      failed_when: "'invalid' in recheck_cert_expiration.stdout"
      when: ('invalid' in cert_expiration.stdout) or (force_cert_renewal)

    
    - name: Restart the Kubelet service
      ansible.builtin.systemd:
        name: kubelet
        state: restarted
      tags:
        - systemd


    - name: Get Cluster information
      kubernetes.core.k8s_cluster_info:
        kubeconfig: "{{ kubeadmin_config }}"
      register: recheck_api_status
      when: ('invalid' in cert_expiration.stdout) or (force_cert_renewal)
      retries: 10
      delay: 10
      until: recheck_api_status is success

    - name: Check if cluster is up and running
      debug:
        msg: "{{ recheck_api_status }}"
      failed_when: recheck_api_status.failed != false
      when: ('invalid' in cert_expiration.stdout) or (force_cert_renewal)
