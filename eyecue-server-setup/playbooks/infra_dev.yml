---
- name: Fingermark Computer Vision Server
  hosts: "{{ run_hosts | split(',') | default('localhost')}}"

  become: yes


  roles:
    - role: "fm.users"
      tags: "access"

    - role: "fingermark.nvidia-driver"
      tags: "nvidia"
      vars:
        nvidia_driver_version: latest
        nvidia_driver_use_ppa: ppa:graphics-drivers/ppa

    - role: "geerlingguy.docker"
      tags: "docker"
      vars:
        docker_version: "5:19.03.10~3-0~ubuntu-bionic"

    - role: "fingermark.nvidia-docker"
      tags: "nvidia"
    
    - role: "fm.mount_drive"
      tags: "storage"

    - role: "mail-notifications"
      tags: "monitoring"

    - role: "fm.softether"
      tags: "vpn"
      vars:
        softether_virtualhub: "servers"
        vpn_user_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          34393637356363323538323230643831626164346538303062386262333864663033306537333366
          3238393530623162346332326436333133333234323865330a383964363232616361643865376566
          37626665393531653663303631623430343434633035323133613037653739346234656337353833
          3333653936333662310a343366623837333035333434663639386661636664366131363563393031
          3666


  tasks:
  - name: Change hostname
    hostname:
      name: "{{ hostname }}"
    tags: basics

  - lineinfile:
      path: /etc/hosts
      line: "*********\t{{ hostname }}\t{{ hostname }}.eyeq.vpn" 
      regexp: "^*********"
    tags: basics