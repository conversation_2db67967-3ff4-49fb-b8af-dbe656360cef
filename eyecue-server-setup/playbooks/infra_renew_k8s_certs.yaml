---
- name: <PERSON>w <PERSON> Certificates
  hosts: "{{ run_hosts.split(',') }}"
  become: true
  gather_facts: true
  tasks:
    - name: Execute the command to renew Kubernetes certificates
      ansible.builtin.shell:
        cmd: "bash /etc/icinga2/scripts/renew-k8s-certs"
      register: renew_output

    - name: Display the output of the command
      ansible.builtin.debug:
        var: renew_output.stdout_lines