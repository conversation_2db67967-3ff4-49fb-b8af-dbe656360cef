---
- name: <PERSON><PERSON><PERSON> and <PERSON><PERSON> Kubernetes Job from CronJob
  hosts: "{{ run_hosts.split(',') }}"
  become: true

  vars:
    infra_namespace: "infra"
    eyecue_namespace: "nmp-{{ ansible_hostname | replace('.eyeq.vpn', '') | trim }}"
    cronjob_name: "ecr-cred-updater"
    infra_job_name: "manual-ecr-cred-updater-inf-ansible"
    eyecue_job_name: "manual-ecr-cred-updater-lc-ansible"
    kubeconfig_file: "/etc/kubernetes/admin.conf"

  tasks:
    - name: Check if Job already exists
      command: >
        kube<PERSON>l get job {{ infra_job_name }} -n {{ infra_namespace }}
      environment:
        KUBECONFIG: "{{ kubeconfig_file }}"
      register: check_job
      failed_when: false
      changed_when: false

    - name: Delete existing Job if it exists
      command: >
        kubectl delete job {{ infra_job_name }} -n {{ infra_namespace }}
      environment:
        KUBECONFIG: "{{ kubeconfig_file }}"
      when: check_job.rc == 0

    - name: Create Job from existing CronJob for Infra namespace
      command: >
        kube<PERSON>l create job --from=cronjob/{{ cronjob_name }} {{ infra_job_name }} -n {{ infra_namespace }}
      environment:
        KUBECONFIG: "{{ kubeconfig_file }}"
      register: create_job_output
      failed_when: "'NotFound' in create_job_output.stderr"

    - name: Display job creation result
      debug:
        msg: "{{ create_job_output.stdout if create_job_output.rc == 0 else create_job_output.stderr }}"

    - name: Verify Job creation
      command: >
        kubectl get job {{ infra_job_name }} -n {{ infra_namespace }}
      environment:
        KUBECONFIG: "{{ kubeconfig_file }}"
      register: verify_job

    - name: Display job verification result
      debug:
        msg: "{{ verify_job.stdout }}"
    
    - name: Check if Job already exists
      command: >
        kubectl get job {{ eyecue_job_name }} -n {{ infra_namespace }}
      environment:
        KUBECONFIG: "{{ kubeconfig_file }}"
      register: check_job
      failed_when: false
      changed_when: false

    - name: Delete existing Job if it exists
      command: >
        kubectl delete job {{ eyecue_job_name }} -n {{ infra_namespace }}
      environment:
        KUBECONFIG: "{{ kubeconfig_file }}"
      when: check_job.rc == 0

    - name: Create Job from existing CronJob for Eyecue namespace
      command: >
        kubectl create job --from=cronjob/{{ cronjob_name }} {{ eyecue_job_name }} -n {{ eyecue_namespace }}
      environment:
        KUBECONFIG: "{{ kubeconfig_file }}"
      register: create_job_output
      failed_when: "'NotFound' in create_job_output.stderr"

    - name: Display job creation result
      debug:
        msg: "{{ create_job_output.stdout if create_job_output.rc == 0 else create_job_output.stderr }}"

    - name: Verify Job creation
      command: >
        kubectl get job {{ eyecue_job_name }} -n {{ eyecue_namespace }}
      environment:
        KUBECONFIG: "{{ kubeconfig_file }}"
      register: verify_job

    - name: Display job verification result
      debug:
        msg: "{{ verify_job.stdout }}"