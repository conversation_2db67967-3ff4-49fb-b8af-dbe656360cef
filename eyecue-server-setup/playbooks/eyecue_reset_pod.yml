---
- name: Delete pods from an Eyecue Kubernetes component
  become: true
  hosts: "{{ run_hosts.split(',')}}"
  vars:
    run_hosts: "localhost"
    kubeconfig_file: "/etc/kubernetes/admin.conf"
    component_name: "tracker"
    label_map:
      argus:
        - "app=eyecue-argus"
      tracker:
        - "app=eyeq-tracker"
      server:
        - "app=eyeq-server"
      detector:
        - "app=eyeq-detector"
      mongodb:
        - "app=mongo"
      triton:
        - "app=triton"
      mosaic:
        - "app=eyecue-mosaic-assembler"
        - "run=eyecue-mosaic-recorder"

  pre_tasks:
    - name: "Installing dependencies"
      ansible.builtin.apt:
        name: python3-pip
    - name: Installing Kubernetes Python library
      ansible.builtin.pip:
        name: kubernetes
      register: kubernetes_package_state
      ignore_errors: true
    - name: Upgrading PyYaml Python Library
      ansible.builtin.pip:
        name: pyyaml
        state: latest
        extra_args: "--ignore-installed"
      when: kubernetes_package_state.failed
    - name: Reinstalling Kubernetes Python library
      ansible.builtin.pip:
        name: kubernetes
      register: kubernetes_package_state
      when: kubernetes_package_state.failed

  tasks:
    - name: "Checking if component name is valid"
      ansible.builtin.fail:
        msg: |
          Component name '{{ component_name }}' is not valid. 
          Valid component names are: {{ label_map.keys() | join(', ') }}
      when: component_name not in label_map.keys()

    - name: Getting Eyecue component pods
      kubernetes.core.k8s_info:
        kubeconfig: "{{ kubeconfig_file }}"
        kind: Pod
        api_version: v1
        namespace: ""  # All namespaces
        label_selectors: "{{ item }}"
      loop: "{{ label_map[component_name] }}"
      register: deployment_pods

    - name: Set Pod Facts
      ansible.builtin.set_fact:
        # convert list of lists to a single list of resources
        deployment_pods: "{{ deployment_pods.results | map(attribute='resources') | flatten(levels=1) }}"

    - name: "Checking if pods are found"
      ansible.builtin.fail:
        msg: "No pods found for component {{ component_name }} with labels: {{ label_map[component_name] | join(', ') }}"
      when: deployment_pods | length == 0

    - name: Delete {{ deployment_pods | length }} pods from deployment
      kubernetes.core.k8s:
        kubeconfig: "{{ kubeconfig_file }}"
        state: absent
        kind: "{{ item.kind }}"
        api_version: v1
        namespace: "{{ item.metadata.namespace }}"
        name: "{{ item.metadata.name }}"
      loop: "{{ deployment_pods }}"
      loop_control:
        label: "{{ item.metadata.name }}"
