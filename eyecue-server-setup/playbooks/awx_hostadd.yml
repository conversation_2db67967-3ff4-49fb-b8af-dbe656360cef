---
- name: This playbook to add hosts to the inventories in awx
  hosts: 127.0.0.1
  vars:
    central_infra_url: "central.infra.fingermark.tech"
    customer:  
      {
        "ana": "3",
        "czp": "5",
        "elj": "7",
        "grd": "8",
        "hrd": "9",
        "infra": "10",
        "kfc": "11",
        "mcd": "12",
        "mcd_nz": "13",
        "nod": "14",
        "qa": "15",
        "stb": "16",
        "poc": "35"
      }



  tasks:
    - name: Update eyecue-server-setup 
      ansible.builtin.uri:
        url: "https://{{ central_infra_url }}/awx/api/v2/projects/8/update/"
        method: POST
        body_format: json
        body: {
              "id": "8"
          }
        headers:
          Authorization: "Bearer {{awx_bearer_token}}"
          Content-Type: application/json
        status_code: 202

    - name: Wait for project to sync
      wait_for:
        delay: 10
        timeout: 0

    - name: Get eyecue-server-setup revision
      ansible.builtin.uri:
        url: "https://{{ central_infra_url }}/awx/api/v2/projects/8/"
        method: GET
        body_format: json
        headers:
          Authorization: "Bearer {{awx_bearer_token}}"
          Content-Type: application/json
        status_code: 200

    - name: Adding hosts to awx inventories
      ansible.builtin.uri:
        url: "https://{{ central_infra_url }}/awx/api/v2/inventories/{{customer[selected_inventory] }}/hosts/"
        method: POST
        body_format: json
        body: {
              "name": "{{ new_host }}"
          }
        headers:
          Authorization: "Bearer {{awx_bearer_token}}"
          Content-Type: application/json
        status_code: 201
      register: host_response


    - name: Associating Host to Group
      ansible.builtin.uri:
        url: "https://{{ central_infra_url }}/awx/api/v2/hosts/{{host_response.json.id}}/groups/"
        method: POST
        body_format: json
        body: {
              "name": "{{ display_name }}",
              "inventory": "{{ customer[selected_inventory] }}",
              "variables": "{display_name: '{{ display_name }}',timezone: '{{ timezone }}',coordinates: '{{ coordinates }}'}"
          }
        headers:
          Authorization: "Bearer {{awx_bearer_token}}"
          Content-Type: application/json
        status_code: 201