---
- name: "Reset Kubernetes cluster using kubeadm reset"
  hosts: "{{ run_hosts.split(',') }}"
  gather_facts: true
  become: true

  collections:
    - community.docker
  vars:
    run_hosts: "localhost"
    delete_media_files: false
    reset_docker: false

  vars_prompt:
    - name: continue
      prompt: You are about to reset docker and Kubernetes cluster. Do you want to continue? (y/N)
      private: false

  tasks:
    - name: "Waiting for user input..."
      ansible.builtin.fail:
      when: "continue != 'y'"

    - name: Remove /media content
      ansible.builtin.file:
        path: /media/fingermark/storage/*
        state: absent
      when: delete_media_files

    - name: Running kubeadm reset...
      ansible.builtin.command: "kubeadm reset --force"

    - name: Deleting kubernetes files and directories
      ansible.builtin.file:
        path: "{{ item }}"
        state: absent
      loop:
        - "/var/lib/etcd/"
        - "/etc/kubernetes/"
        - "/etc/cni/net.d/"
        - "/etc/kubernetes/kubelet.conf"
        - "/etc/kubernetes/pki/ca.crt"

    - name: "Removing Docker from the system"
      when: reset_docker
      block:
        - name: Prune everything
          community.docker.docker_prune:
            containers: true
            images: true
            networks: true
            volumes: true
            builder_cache: true
          tags:
            - docker

        - name: "Removing docker packages"
          ansible.builtin.apt:
            name:
              - docker-ce
              - docker-ce-cli
              - docker-ce-rootless-extras
            state: absent
            purge: true
            autoremove: true
          tags:
            - docker
        - name: Reboot host and wait for it to restart
          ansible.builtin.reboot:
            msg: "Reboot initiated by Ansible"
            connect_timeout: 5
            reboot_timeout: 600
            pre_reboot_delay: 0
            post_reboot_delay: 30
            test_command: whoami
          tags:
            - docker

        - name: Delete docker container files
          ansible.builtin.file:
            path:
            state: absent
          loop:
            - "/var/lib/docker"
            - "/etc/docker"
            - "/etc/apparmor.d/docker"
            - "/var/run/docker.sock"
          tags:
            - docker

        - name: Reinstall Docker
          ansible.builtin.include_role:
            name: geerlingguy.docker
          vars:
            docker_version: "5:20.10.14~3-0~ubuntu-{{ ansible_distribution_release | lower }}"
          tags:
            - docker
        - name: Installing Nvidia Container Runtime
          ansible.builtin.include_role:
            name: nvidia_gpu_operator
          tags:
            - nvidia
            - runtime
            - drivers

        - name: Setup nvidia-docker again
          ansible.builtin.include_role:
            name: fingermark.nvidia-docker
          tags:
            - nvidia
            - nvidia-docker     
