---
- name: Delete sensitive files
  become: true
  gather_facts: true
  hosts: "{{ run_hosts.split(',') }}"

  tasks:
    - name: Ensure Ansible vault token is absent
      file:
        path: /etc/ansible-agent/vault.txt
        state: absent

    - name: Ensure Ansible id_rsa file is absent
      file:
        path: /etc/ansible-agent/id_rsa
        state: absent

    - name: Ensure file Kommisjon config.ini is absent
      file:
        path: /opt/kommisjon/config.ini
        state: absent

    - name: Ensure file Kommisjon bootstrap crontask is absent (kommisjon-bootstrap.sh)
      file:
        path: /etc/cron.d/kommisjon-bootstrap
        state: absent

    - name: Ensure file Kommisjon bootstrap crontask is absent (spore.py)
      file:
        path: /etc/cron.d/kommisjon
        state: absent
