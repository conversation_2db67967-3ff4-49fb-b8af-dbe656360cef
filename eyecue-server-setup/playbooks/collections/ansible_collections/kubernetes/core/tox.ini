[tox]
minversion = 1.4.2
skipsdist = True

[testenv:integration]
install_command = pip install {opts} {packages}

deps =  -r{toxinidir}/requirements.txt
        -r{toxinidir}/test-requirements.txt
        https://github.com/ansible/ansible/archive/devel.tar.gz

passenv =
    HOME

commands=
    ansible-test integration -v --color --retry-on-error --diff --coverage --continue-on-error --python {posargs}

[testenv:add_docs]
deps = git+https://github.com/ansible-network/collection_prep
commands = collection_prep_add_docs -p .

[testenv:black]
deps =
  black >= 22.0, < 23.0

commands =
  black -v --check --diff {toxinidir}/plugins {toxinidir}/tests

[testenv:linters]
deps =
  yamllint
  flake8
  {[testenv:black]deps}

commands =
  black -v --check --diff {toxinidir}/plugins {toxinidir}/tests
  yamllint -s {toxinidir}
  flake8 {toxinidir}
