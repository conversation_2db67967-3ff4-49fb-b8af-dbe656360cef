---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

DOCUMENTATION:
  name: k8s_config_resource_name
  short_description: Generate resource name for the given resource of type ConfigMap, Secret
  description:
    - Generate resource name for the given resource of type ConfigMap, Secret.
    - Resource must have a C(metadata.name) key to generate a resource name
  options:
    _input:
      description:
        - A valid YAML definition for a ConfigMap or a Secret.
      type: dict
      required: true
  author:
    - ansible cloud team

EXAMPLES: |
  # Dump generated name for a configmap into a variable
  - set_fact:
      generated_name: '{{ definition | kubernetes.core.k8s_config_resource_name }}'
    vars:
      definition:
        apiVersion: v1
        kind: ConfigMap
        metadata:
          name: myconfigmap
          namespace: mynamespace

RETURN:
  _value:
    description: Generated resource name.
    type: str
