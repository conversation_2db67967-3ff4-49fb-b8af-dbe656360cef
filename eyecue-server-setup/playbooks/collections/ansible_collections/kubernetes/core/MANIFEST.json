{"collection_info": {"namespace": "kubernetes", "name": "core", "version": "2.4.0", "authors": ["chouseknecht (https://github.com/chouseknecht)", "geerlingguy (https://www.jeffgeerling.com/)", "maxamillion (https://github.com/maxamillion)", "jmontleon (https://github.com/jmontleon)", "fabianvf (https://github.com/fabianvf)", "willthames (https://github.com/willthames)", "mmazur (https://github.com/mmazur)", "jamescassell (https://github.com/jamescassell)"], "readme": "README.md", "tags": ["kubernetes", "k8s", "cloud", "infrastructure", "openshift", "okd", "cluster"], "description": "Kubernetes Collection for Ansible.", "license": [], "license_file": "LICENSE", "dependencies": {}, "repository": "https://github.com/ansible-collections/kubernetes.core", "documentation": "", "homepage": "", "issues": "https://github.com/ansible-collections/kubernetes.core/issues"}, "file_manifest_file": {"name": "FILES.json", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "67908745863fefe0495eee77399178e88b89bb04a19fc728d2ac83a4d93abd1a", "format": 1}, "format": 1}