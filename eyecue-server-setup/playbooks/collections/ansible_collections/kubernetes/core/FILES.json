{"files": [{"name": ".", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": ".github", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": ".github/patchback.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "ed69f87ea46171cb574fb77dc74fdbd7a269d4cad8d5ba6494d64d99842ef8e4", "format": 1}, {"name": ".github/stale.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "923b49f6fb8b325ea890d05a42537b3f9c5aaf26b64a704c0fef4b696aa6a4bb", "format": 1}, {"name": "changelogs", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "changelogs/changelog.yaml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "e116a242c550ab4f2dde8c5d0db5f75085b34a39160cfa6c3ffd180d21b4c07e", "format": 1}, {"name": "changelogs/config.yaml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "2903835efadf1f03f8a05ba8428d1530259e322d039dcd3edbe707bcaea82e3d", "format": 1}, {"name": "docs", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "docs/docsite", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "docs/docsite/rst", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "docs/docsite/rst/kubernetes_scenarios", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "docs/docsite/rst/kubernetes_scenarios/k8s_intro.rst", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "7ce06411318e79c00c9c46d0faf31f34de731bff5ef22e00b57c6e8f26c2dac8", "format": 1}, {"name": "docs/docsite/rst/kubernetes_scenarios/k8s_inventory.rst", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "29e7c5d13c2ea6ed35247acf7a05a1008662f950ff79c8bc2a9b41887d004975", "format": 1}, {"name": "docs/docsite/rst/kubernetes_scenarios/k8s_scenarios.rst", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "b98746ba71dce9ba385228e55c7c367888a3fc569bfa37261475bc24179406a8", "format": 1}, {"name": "docs/docsite/rst/kubernetes_scenarios/scenario_k8s_object.rst", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "b7a4b5b5c86e756523c86c7aba91b768872f3b3af9fb5b0bc37aae6139dfd991", "format": 1}, {"name": "docs/docsite/rst/scenario_guide.rst", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "d14c604f7f6b2311f9b9688dcea5d18357920d7ccd228539b2cf0e52a997c822", "format": 1}, {"name": "docs/docsite/extra-docs.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "79c40762026c71888c65490babe92e16d252af7113dec735cf75630af8245d47", "format": 1}, {"name": "docs/ansible_turbo_mode.rst", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "f2b28caa1289a1267341d148134c9fc6544a05eb5146d80a51da6703d995cecb", "format": 1}, {"name": "docs/kubernetes.core.helm_info_module.rst", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "89adae8f40c2da10c350d9cfc1b3844104f9c8d51da2fbf13b9301cad91b5a96", "format": 1}, {"name": "docs/kubernetes.core.helm_module.rst", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "a8e5c2ca7aa70bca9641dd290814de18d2ade93939cdbae64a7b7484a5418565", "format": 1}, {"name": "docs/kubernetes.core.helm_plugin_info_module.rst", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "51ba9800d8f92207fb2580158d75a05c942882fe237dfdbceb1b16014e13ef5d", "format": 1}, {"name": "docs/kubernetes.core.helm_plugin_module.rst", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "b9ed32e51ca564332c6c070793ed4950b9c52394f62f37416658988d5d16efd0", "format": 1}, {"name": "docs/kubernetes.core.helm_pull_module.rst", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "ca57afa3fe3b2c69f44bc948c2088d7056d197b65281107bc9ce32d31c1b3adf", "format": 1}, {"name": "docs/kubernetes.core.helm_repository_module.rst", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "4c7b2efcf6832cf1b53e9fec808449ae5ea973306ae2a3eeeed4ea7405327e06", "format": 1}, {"name": "docs/kubernetes.core.helm_template_module.rst", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "70f91cecfcc664c8ac35b58ce351fbf2a8f3051608580b180c830ca617b4f856", "format": 1}, {"name": "docs/kubernetes.core.k8s_cluster_info_module.rst", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "061da0cf6ce62e3ba49491f11533d76e4dc8073ca8a9dbee2d3c981ad56f3d70", "format": 1}, {"name": "docs/kubernetes.core.k8s_cp_module.rst", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "3cb1cc05abdeaa923c87b17cff8b7c6d2aab7273b67ef6879f594cfc5ec1489d", "format": 1}, {"name": "docs/kubernetes.core.k8s_drain_module.rst", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "b8e412c8036aa76e0ae785941b8bf317ab71296d910da6750b3793271607533c", "format": 1}, {"name": "docs/kubernetes.core.k8s_exec_module.rst", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "5b0c29c1755d04f7be9391da36a1803b6040f18fe7efdd72a14051ad758ed9e5", "format": 1}, {"name": "docs/kubernetes.core.k8s_info_module.rst", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "d26adc890f69739b4744ad88a1757cbf68fca96cf9b1394a7614ffc6760732b8", "format": 1}, {"name": "docs/kubernetes.core.k8s_inventory.rst", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "f0e7072fe1ebf5e12870ba93e0de9bb571f3538322d60ee6925ac025023b4ff4", "format": 1}, {"name": "docs/kubernetes.core.k8s_json_patch_module.rst", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "b628ee67825e39bf7517eae54e7c322968719807cf349ed650674c99c334607a", "format": 1}, {"name": "docs/kubernetes.core.k8s_log_module.rst", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "407fd3cf0c037d5a5226c554f5d318e549b9e9fb862b9835d958612a0f515f27", "format": 1}, {"name": "docs/kubernetes.core.k8s_lookup.rst", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "fac344797caf1dcdf99adeec0a0d850d1fd74fbf46f3a226b52931f1fa6bca0d", "format": 1}, {"name": "docs/kubernetes.core.k8s_module.rst", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "890765b7b747dc2762b0683c704fec4d18ddac7daccb68e28a75cf9f79a47893", "format": 1}, {"name": "docs/kubernetes.core.k8s_rollback_module.rst", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "9fd9867dcd6be0868d7fb419b9ce930f6ecbbbf66af9e4ec22ba3c3ce8151d5c", "format": 1}, {"name": "docs/kubernetes.core.k8s_scale_module.rst", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "89d2cb58fcea0081652fd6a72f2370900cd9cd54b4444cc6a1cced61d505127e", "format": 1}, {"name": "docs/kubernetes.core.k8s_service_module.rst", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "a766b619b69267f29cab51baf780de200fa7fa2377fcda92585657e1dc849d0f", "format": 1}, {"name": "docs/kubernetes.core.k8s_taint_module.rst", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "d64a4250bdca5ac386fa4539fa837c7d89a26e63d24fc01777d1f2125b70f160", "format": 1}, {"name": "docs/kubernetes.core.kubectl_connection.rst", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "7cb93572ffd283a453b53bd8d100ce0949a6a6cdce850c5deecd22cafa0eb4e8", "format": 1}, {"name": "docs/kubernetes.core.kustomize_lookup.rst", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "b6006ecdeaec488a59651653d7c48c0f4d156f49fc3a12409f75a187eafb57b7", "format": 1}, {"name": "meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "meta/runtime.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "234b418a0a39617e8ab74b92c221dddf0b8705b36fc755c0ba70d8e070809365", "format": 1}, {"name": "plugins", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "plugins/action", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "plugins/action/helm.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "1c8c0b75ee4f8568f7eb0aa93fbf0b1daeb8eb1ffe9d92e8cfc76a4e5d47cc20", "format": 1}, {"name": "plugins/action/helm_info.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "1c8c0b75ee4f8568f7eb0aa93fbf0b1daeb8eb1ffe9d92e8cfc76a4e5d47cc20", "format": 1}, {"name": "plugins/action/helm_plugin.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "1c8c0b75ee4f8568f7eb0aa93fbf0b1daeb8eb1ffe9d92e8cfc76a4e5d47cc20", "format": 1}, {"name": "plugins/action/helm_plugin_info.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "1c8c0b75ee4f8568f7eb0aa93fbf0b1daeb8eb1ffe9d92e8cfc76a4e5d47cc20", "format": 1}, {"name": "plugins/action/helm_repository.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "1c8c0b75ee4f8568f7eb0aa93fbf0b1daeb8eb1ffe9d92e8cfc76a4e5d47cc20", "format": 1}, {"name": "plugins/action/k8s.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "1c8c0b75ee4f8568f7eb0aa93fbf0b1daeb8eb1ffe9d92e8cfc76a4e5d47cc20", "format": 1}, {"name": "plugins/action/k8s_cluster_info.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "1c8c0b75ee4f8568f7eb0aa93fbf0b1daeb8eb1ffe9d92e8cfc76a4e5d47cc20", "format": 1}, {"name": "plugins/action/k8s_cp.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "1c8c0b75ee4f8568f7eb0aa93fbf0b1daeb8eb1ffe9d92e8cfc76a4e5d47cc20", "format": 1}, {"name": "plugins/action/k8s_drain.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "1c8c0b75ee4f8568f7eb0aa93fbf0b1daeb8eb1ffe9d92e8cfc76a4e5d47cc20", "format": 1}, {"name": "plugins/action/k8s_exec.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "1c8c0b75ee4f8568f7eb0aa93fbf0b1daeb8eb1ffe9d92e8cfc76a4e5d47cc20", "format": 1}, {"name": "plugins/action/k8s_log.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "1c8c0b75ee4f8568f7eb0aa93fbf0b1daeb8eb1ffe9d92e8cfc76a4e5d47cc20", "format": 1}, {"name": "plugins/action/k8s_rollback.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "1c8c0b75ee4f8568f7eb0aa93fbf0b1daeb8eb1ffe9d92e8cfc76a4e5d47cc20", "format": 1}, {"name": "plugins/action/k8s_scale.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "1c8c0b75ee4f8568f7eb0aa93fbf0b1daeb8eb1ffe9d92e8cfc76a4e5d47cc20", "format": 1}, {"name": "plugins/action/k8s_service.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "1c8c0b75ee4f8568f7eb0aa93fbf0b1daeb8eb1ffe9d92e8cfc76a4e5d47cc20", "format": 1}, {"name": "plugins/action/ks8_json_patch.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "1c8c0b75ee4f8568f7eb0aa93fbf0b1daeb8eb1ffe9d92e8cfc76a4e5d47cc20", "format": 1}, {"name": "plugins/action/k8s_info.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "1c8c0b75ee4f8568f7eb0aa93fbf0b1daeb8eb1ffe9d92e8cfc76a4e5d47cc20", "format": 1}, {"name": "plugins/connection", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "plugins/connection/kubectl.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "5895e521f261d2172764464c56e886ce61652bb8f9d0b88b51afb909da5d159f", "format": 1}, {"name": "plugins/doc_fragments", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "plugins/doc_fragments/__init__.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "format": 1}, {"name": "plugins/doc_fragments/helm_common_options.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "a66b232cb2fa3c8a28498bd30389ca8f4ffd7ae97b70bfe601788d46779f9cb3", "format": 1}, {"name": "plugins/doc_fragments/k8s_auth_options.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "8d189a6ab2d0937cd572d2510823ecbe1a9742d5bed23a6406eae926aa1f7f7d", "format": 1}, {"name": "plugins/doc_fragments/k8s_delete_options.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "3e47df1b0ac656ad1b756b0628aeef1bbd47baa2dfbff643fd151467d0d18c97", "format": 1}, {"name": "plugins/doc_fragments/k8s_name_options.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "293ef90ac4a1ad52677f22c747dda0d9e740c1a8e8c1fc783d110c2bbb35dc76", "format": 1}, {"name": "plugins/doc_fragments/k8s_resource_options.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "30875d624f093085908955fb0ff53ab73602e5dffa66cbcba32de0a3e48785b3", "format": 1}, {"name": "plugins/doc_fragments/k8s_scale_options.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "81ae1fe5c034a916cba915be2a35406d56340ead6a3189963911ebc93796d322", "format": 1}, {"name": "plugins/doc_fragments/k8s_state_options.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "569e138705ec9b5bf2ba429cc0e71e87e4459cd1289470fb71e7436cd949f1c3", "format": 1}, {"name": "plugins/doc_fragments/k8s_wait_options.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "e3c86e0272f62fbfc49c1c5eeeb4d7c61d9726bcec5e8965ae5c77edcab97296", "format": 1}, {"name": "plugins/filter", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "plugins/filter/k8s.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "1732300a6c48e6e6e678ea2b1dffb4a22779c6c7fe04f9fe64fd397df08af7c1", "format": 1}, {"name": "plugins/filter/k8s_config_resource_name.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "47eb4fec78642ef7f76fe378beeeed7a2bb2bb344838adcc1ac6f178578ee7ee", "format": 1}, {"name": "plugins/inventory", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "plugins/inventory/k8s.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "246cedb5fb08aa567cb266fcf9ac6513dffaa68ea75e33e8f2fa7e23cb12f9d5", "format": 1}, {"name": "plugins/lookup", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "plugins/lookup/k8s.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "2b24cf87c1463e091f41879aa2117b532dc481b74f7e9999cd049cdb5a8ee1ac", "format": 1}, {"name": "plugins/lookup/kustomize.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "cf1be05e740300ccb2d9a64f89024f8e645e9a3f33d921abd4c9e7e139645785", "format": 1}, {"name": "plugins/module_utils", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "plugins/module_utils/client", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "plugins/module_utils/client/discovery.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "9e91dfdcba133e090d1cba79f827ee09ed1a3915eee3e08b526e79ccdcac37c1", "format": 1}, {"name": "plugins/module_utils/client/resource.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "5ecbd5410095b770939270966962c5e939b7e662eb6f51c1271d294e6e683ee2", "format": 1}, {"name": "plugins/module_utils/k8s", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "plugins/module_utils/k8s/client.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "f7428c830e53238c9682715651d3a125d172bea223970d851cc823291bb84c17", "format": 1}, {"name": "plugins/module_utils/k8s/core.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "55a0a8cceda4e9ca604fe724af5d6cf82d9e382d138b110ac5d7ba9b073549d5", "format": 1}, {"name": "plugins/module_utils/k8s/exceptions.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "68fc2551bb7c65b1d0352c6761d2f75a6ca59fa773f08faa3356ab4787c040eb", "format": 1}, {"name": "plugins/module_utils/k8s/resource.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "bf5d3132b51a4b725c11cd9a69bc75274663059234b56a6231271bf8c23bcaef", "format": 1}, {"name": "plugins/module_utils/k8s/runner.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "9d1a9e22e30f2a1faf0d84687d5181517fda5844c97003e0d24e4b024d7412dd", "format": 1}, {"name": "plugins/module_utils/k8s/service.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "9f3c774651fac14bfd191bff16e5c374049dd434880d63ca9a7a1b4dbde7bd7b", "format": 1}, {"name": "plugins/module_utils/k8s/waiter.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "c5c11459d7e02516b111cb64a5e31cf3afa1d9a9ffadfbc87ab477d5684d49d7", "format": 1}, {"name": "plugins/module_utils/__init__.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "format": 1}, {"name": "plugins/module_utils/_version.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "da42772669215aa2e1592bfcba0b4cef17d06cdbcdcfeb0ae05e431252fc5a16", "format": 1}, {"name": "plugins/module_utils/ansiblemodule.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "e6a42da6322ac61fc8c0d86a9b60c4ad4255a2ba19d0c69a139c80ace2773e4f", "format": 1}, {"name": "plugins/module_utils/apply.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "d13b411d95075d9aca971d7c4badd496a981011903d08f93ae0cd62f73968d10", "format": 1}, {"name": "plugins/module_utils/args_common.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "fdb406f9194a6a3cd97d6d805fb02ffd9d51e4209d7bac04c2c7c38ebe99dff2", "format": 1}, {"name": "plugins/module_utils/common.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "1ba4d32d44ef9366cb5f181a91692f269aac5f4ccc4c971237ac720a1c47cbe3", "format": 1}, {"name": "plugins/module_utils/copy.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "e225cb19d811f0eb677f624db73f65ff5ead23e3701b7a266a9d5d03a3e99151", "format": 1}, {"name": "plugins/module_utils/exceptions.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "4256700ac9b1b0b29a0daa8d24da068a8435413cdd927b9613f4fa568e5ee450", "format": 1}, {"name": "plugins/module_utils/hashes.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "8495aceb22009f8529c1f5a82f4c338d9a727d23d0a85f7386183609c9e34c5c", "format": 1}, {"name": "plugins/module_utils/helm.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "03c4cccb138537a8291f32025575c7ccc1a6b903b84a9f492851a1e2d07c2a07", "format": 1}, {"name": "plugins/module_utils/helm_args_common.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "4e062ea6af707d2e71a4bf17c5fdc847bbb7867c53c4d6233ff8644991a136a8", "format": 1}, {"name": "plugins/module_utils/k8sdynamicclient.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "c58236ebcf4a9d39ccce1fb9f0084c716596fa269557553db1d421f9769c1088", "format": 1}, {"name": "plugins/module_utils/selector.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "a912a35f5014bdf2a30625614b05acd2d71fabaea2b980bb4f6b110151e3e345", "format": 1}, {"name": "plugins/module_utils/version.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "c009a2e470b5c1e2cfc73efb061b3289f3da5064c85ad31dd664433ddb7b97b7", "format": 1}, {"name": "plugins/modules", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "plugins/modules/__init__.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "format": 1}, {"name": "plugins/modules/helm.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "1213cd7c02f4e2f28f4126c74e3145dfbd4b04185fb74c3a3b930f49ffbaaac1", "format": 1}, {"name": "plugins/modules/helm_info.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "5aa8e19162522752cd1625cd2953a2d4dfcad29dfb08607d28a62e0929aec8f8", "format": 1}, {"name": "plugins/modules/helm_plugin.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "0425eaf1efe23f6d9eb6812300d0525eee18c769e80c6c74e193ee376c05901b", "format": 1}, {"name": "plugins/modules/helm_plugin_info.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "80794a520c59990974d93c32d813ecee8de50b2cb36ababde183ee5db62c8cfa", "format": 1}, {"name": "plugins/modules/helm_pull.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "da8a930e40e61287c5da5a1e4c15c29907d8330d060b89ecdc3a238855553c03", "format": 1}, {"name": "plugins/modules/helm_repository.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "2d348193a38287251a86a8efb47a52d26fdae877447dc9933a1ee14281e9d3ee", "format": 1}, {"name": "plugins/modules/helm_template.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "c9b5f60a07127da4cdf139bbd83fe728aa66320ae644a1906a39fb87a871502d", "format": 1}, {"name": "plugins/modules/k8s.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "efeb2745cafcf037d68b0a8b7594cd30e2743bddcaed073a864854ede685e162", "format": 1}, {"name": "plugins/modules/k8s_cluster_info.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "a04f656c51d094f1acb7b347d39da19ab3c46b35de9f07f23b9910ae601665f7", "format": 1}, {"name": "plugins/modules/k8s_cp.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "f5cd6f3791bfb5856900f4d3c24bb7dc4c2f895e89c619a0e1480b3634c75330", "format": 1}, {"name": "plugins/modules/k8s_drain.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "c5aa3613e22210c8800b69cdf2dd6847d743efa9fde99a13df60f86a6c2068a1", "format": 1}, {"name": "plugins/modules/k8s_exec.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "3e85e93a987900104d20a7c8ed736df1a720c072dd181d4a092fb5344e08257a", "format": 1}, {"name": "plugins/modules/k8s_info.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "f1c5cefb218b7fc7887213a8905385a9627c02fd228b91238c72061ea6da0cf3", "format": 1}, {"name": "plugins/modules/k8s_json_patch.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "44097a47c792a7899ff04788d0f958a1819194224a5588eb5a5db5f448722be7", "format": 1}, {"name": "plugins/modules/k8s_log.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "4907cd48fae87e481961f29edea0dd460071d73513c3d5bae85dc63d882f4b5e", "format": 1}, {"name": "plugins/modules/k8s_rollback.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "ee962569276d6117500f57ef2bac32e3dc75940855e2f351738318e6700ebd3a", "format": 1}, {"name": "plugins/modules/k8s_scale.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "5486c6a62a1d27585485bef37fb5abde8a477543f7a37c6e42e5d4d65bcd8730", "format": 1}, {"name": "plugins/modules/k8s_service.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "da88e5c089db282cf1413ff002a6e781c7f05a54689c992494b52b9054689a41", "format": 1}, {"name": "plugins/modules/k8s_taint.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "ec21f39fd4efd743655d36a8091c24b7bfcd76af239cb5cb068415fad9552517", "format": 1}, {"name": "tests", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "4f8b1fa71279ad05526d564d79582b3be0ea406397bdde007f7f84cf557fc063", "format": 1}, {"name": "tests/integration/targets/helm/files", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm/files/appversionless-chart-v2", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm/files/appversionless-chart-v2/templates", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm/files/appversionless-chart-v2/templates/configmap.yaml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "9979f18c89ec57fb48ad851cd33e760f0ff1807a47cd4999fc2d562e155fd098", "format": 1}, {"name": "tests/integration/targets/helm/files/appversionless-chart-v2/Chart.yaml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "1c09667591633d997f2a6ef625d6aec9b0854b6fa7ec51bbee42b8ac460d6f3c", "format": 1}, {"name": "tests/integration/targets/helm/files/appversionless-chart", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm/files/appversionless-chart/templates", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm/files/appversionless-chart/templates/configmap.yaml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "18f299d28d316f4f389d902630342476cad9fa39e71b118f9653c2b768671a45", "format": 1}, {"name": "tests/integration/targets/helm/files/appversionless-chart/Chart.yaml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "fa6d86a1333abbc0bce9320952b49ceeda05fed029ab498c2bc9633b0507d62a", "format": 1}, {"name": "tests/integration/targets/helm/files/dep-up", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm/files/dep-up/Chart.yaml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "5b1942642cdfcb72cf9130dcfa4c7db1dab07ca1a2f3bc4eada8dcc6e647a9e3", "format": 1}, {"name": "tests/integration/targets/helm/files/dep-up/values.yaml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "e7e06f6160ada3c85ffe940ad3045be134a2630db4ef337c94136c326acbb795", "format": 1}, {"name": "tests/integration/targets/helm/files/test-chart-v2", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm/files/test-chart-v2/templates", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm/files/test-chart-v2/templates/configmap.yaml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "9979f18c89ec57fb48ad851cd33e760f0ff1807a47cd4999fc2d562e155fd098", "format": 1}, {"name": "tests/integration/targets/helm/files/test-chart-v2/Chart.yaml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "cc34a7fb5f306592164960bda0f3be7160a0a95e0680c26e094e68f9d7b17e61", "format": 1}, {"name": "tests/integration/targets/helm/files/test-chart", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm/files/test-chart/templates", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm/files/test-chart/templates/configmap.yaml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "18f299d28d316f4f389d902630342476cad9fa39e71b118f9653c2b768671a45", "format": 1}, {"name": "tests/integration/targets/helm/files/test-chart/Chart.yaml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "b1d6b7aaa7b3de76b01f935c002b06f7897477042f872aa732d9847894a2e6af", "format": 1}, {"name": "tests/integration/targets/helm/files/test-crds", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm/files/test-crds/crds", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm/files/test-crds/crds/crd.yaml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "e5b364fef4a2265bb39e4cf411bf28e3da6df6c4990c0d18a8b58d253141fd17", "format": 1}, {"name": "tests/integration/targets/helm/files/test-crds/Chart.yaml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "a3098e5531bbeea8677e05205809430b1b65749ef96ffc61c0bbc2ccd534191e", "format": 1}, {"name": "tests/integration/targets/helm/files/values.yaml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "dcaf3f49ec12d4365ac0b9dba73255eeba9fea38895c7e63dc6e1ea0fe1fdb02", "format": 1}, {"name": "tests/integration/targets/helm/library", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm/library/helm_test_version.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "fb24cdcfd0d0e43450817fb51e96c4e78cc9ed74a4d65493a5c31e71398e2fee", "format": 1}, {"name": "tests/integration/targets/helm/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "16bb2a70e1f18f3ee0e6e4f9738a0533f2286c3163f9ba487a2af7c1d77675da", "format": 1}, {"name": "tests/integration/targets/helm/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm/tasks/tests_chart", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm/tasks/tests_chart/from_local_path.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "11d85f5e3f5b0a9d7e487a5907d7c4abfdb9c309146b811336aed11bf87fff17", "format": 1}, {"name": "tests/integration/targets/helm/tasks/tests_chart/from_repository.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "0ac2c2a2df4f9f4ea023bf2bd1655e127282963bd0db0eafe06ced5eef836404", "format": 1}, {"name": "tests/integration/targets/helm/tasks/tests_chart/from_url.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "3a0e7c533c4fd8a0f3876498fcf4cbc43398da6f05fc9559a20f2d0a8822dc7a", "format": 1}, {"name": "tests/integration/targets/helm/tasks/install.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "ade2b4fda2a79580f34163a9c7df12c017d8e38285342c9f281995f3179d477f", "format": 1}, {"name": "tests/integration/targets/helm/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "b0efdc4f0f86755e2f8675141088de4ebbbcc8d7c035248212b20b40a47e0cfb", "format": 1}, {"name": "tests/integration/targets/helm/tasks/run_test.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "951714c0c82ed7edc2c3217592668cd6d0e89dca1b04347d6bd5261ba5475412", "format": 1}, {"name": "tests/integration/targets/helm/tasks/test_crds.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "d3b9e126f8f223d41dd43a7848ed06fafd15b8f5fd0bee7eea896d84a3cdd4e7", "format": 1}, {"name": "tests/integration/targets/helm/tasks/test_helm_not_installed.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "4ad5395bdeccad7828b703759b23d4d689a7e4a926eacf4a30af7caafcad2ace", "format": 1}, {"name": "tests/integration/targets/helm/tasks/test_helm_uninstall.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "8b7b2a8d5ec8bda414225cadc593308480165473842b3d6b7eacbbc77bd68d64", "format": 1}, {"name": "tests/integration/targets/helm/tasks/test_read_envvars.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "43efeb25d57f49283c97e1585d5d05e5d8e52b3a44eae7292aa5c8f11bf04cd3", "format": 1}, {"name": "tests/integration/targets/helm/tasks/test_up_dep.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "2874f5c6212125dfd782d4dd03e39eac0a1f874ec26c28dd0d52b82fcc5f8cd0", "format": 1}, {"name": "tests/integration/targets/helm/tasks/tests_chart.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "49ac759a792169559880988995dead3d5ce25540108e2c9f96fe63aeea26e318", "format": 1}, {"name": "tests/integration/targets/helm/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "a2dfeff186fbdc681ec850d230d827f6c2c874d598e5181313cfcf6800832100", "format": 1}, {"name": "tests/integration/targets/helm_diff", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm_diff/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm_diff/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "e161fd07123767930d87054ce50d82038fd5c25d6b093afc1feb61fb5e6d46d5", "format": 1}, {"name": "tests/integration/targets/helm_diff/files", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm_diff/files/test-chart", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm_diff/files/test-chart/templates", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm_diff/files/test-chart/templates/configmap.yaml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "18f299d28d316f4f389d902630342476cad9fa39e71b118f9653c2b768671a45", "format": 1}, {"name": "tests/integration/targets/helm_diff/files/test-chart/Chart.yaml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "b1d6b7aaa7b3de76b01f935c002b06f7897477042f872aa732d9847894a2e6af", "format": 1}, {"name": "tests/integration/targets/helm_diff/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm_diff/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "c62410da25d2dbb4bc0c82bd69412598750a7b3f12e1dfdf357e750cd7efb78e", "format": 1}, {"name": "tests/integration/targets/helm_diff/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm_diff/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "e369b170ddb55919f665ffb224c8cbef7df5713b8961a9b016524cc0f8b9b43f", "format": 1}, {"name": "tests/integration/targets/helm_diff/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "0fe75a38d2132b50d7539eb71391cf8b0b70203d0e311c5c5be829fd0deccc02", "format": 1}, {"name": "tests/integration/targets/helm_kubeconfig", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm_kubeconfig/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm_kubeconfig/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "b21a19b5f11aca3bfe7d4849b23550f98cb9a38b305e75804c42f92fe9e385ff", "format": 1}, {"name": "tests/integration/targets/helm_kubeconfig/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm_kubeconfig/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "706a0802afe247b21d705adcf9438b038b0d27a20202880fd729aaa46ee4e419", "format": 1}, {"name": "tests/integration/targets/helm_kubeconfig/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm_kubeconfig/tasks/from_in_memory_kubeconfig.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "80c5cb8e54afcfe56edcb20af674842a69bb9de52e06329fdc1a63785799c16d", "format": 1}, {"name": "tests/integration/targets/helm_kubeconfig/tasks/from_kubeconfig_with_cacert.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "a359babf91296ee84bf38cff248124abc310a72f8f6ad2e0f2feb5cfebde278f", "format": 1}, {"name": "tests/integration/targets/helm_kubeconfig/tasks/from_kubeconfig_with_validate_certs.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "df195f27dc10ab15f13e55c672ab0d7093ead0cbab05e265cd61cea1179a1f33", "format": 1}, {"name": "tests/integration/targets/helm_kubeconfig/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "fed1323a4ae00ebe0c13dda54739ccf4d2188a901d5a27080594e9c54d16d498", "format": 1}, {"name": "tests/integration/targets/helm_kubeconfig/tasks/tests_helm_auth.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "d5cdbfb44509f3e21acd4ee351e1d28c9564b8b53369de25d4ce9c5316e88f8a", "format": 1}, {"name": "tests/integration/targets/helm_kubeconfig/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "6881d2c3d8e4323d9010284025fe44d0512f308fbf95057b4f25f7a7c6ab4cee", "format": 1}, {"name": "tests/integration/targets/helm_plugin", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm_plugin/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm_plugin/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "54fe0fec62aa8599fd6ff15e47ae9909281f62bd0d1b7a2c2ef4583fecf7f5d8", "format": 1}, {"name": "tests/integration/targets/helm_plugin/files", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm_plugin/files/sample_plugin", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm_plugin/files/sample_plugin/plugin.yaml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "1a0e33cd8669e3052458cb1a25a1af6bd9ef76e58eca5e3720637e8ab516111d", "format": 1}, {"name": "tests/integration/targets/helm_plugin/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm_plugin/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "9bba6e26661bb086d7e9db12fe0b2343f1cd83fda97bc6275c6d226711008732", "format": 1}, {"name": "tests/integration/targets/helm_plugin/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm_plugin/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "22ca81c5a2d1a08b046f70ec3266e2b18c51a0bb10957b354fbf840bf79d1ca8", "format": 1}, {"name": "tests/integration/targets/helm_plugin/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "f9dd7470d4b1bf31b0664a480955a121f2f83105f78f820e649cf6f6822fde5d", "format": 1}, {"name": "tests/integration/targets/helm_pull", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm_pull/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm_pull/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "d6b0ed170fb58beac78bbbda4ef240a1dad8291b3f4eae68cfa44535527fbc23", "format": 1}, {"name": "tests/integration/targets/helm_pull/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "9324d1375534c1b2cd31e7d392548e1d7fb8c4b96b8bda1a5fd8d2e6d03521e5", "format": 1}, {"name": "tests/integration/targets/helm_repository", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm_repository/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm_repository/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "f97c6217ef12599cfcbb953747ac534cb9967e8530c268e6ed56a0bf51e9613e", "format": 1}, {"name": "tests/integration/targets/helm_repository/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm_repository/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "9bba6e26661bb086d7e9db12fe0b2343f1cd83fda97bc6275c6d226711008732", "format": 1}, {"name": "tests/integration/targets/helm_repository/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm_repository/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "5a261c0f6b12f0bf72deb2f49a576cbefaf9570db7e3fbd9251b32768527f2b2", "format": 1}, {"name": "tests/integration/targets/helm_repository/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "6bdfe5ff6ae8ca3ee241def5499a8b7fbe977142e7381934c73b709784123607", "format": 1}, {"name": "tests/integration/targets/helm_set_values", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm_set_values/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm_set_values/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "7bb5d1f3124e2053aeb54c5c40cb54587386a3f0a338f28f80a039bef6c7bd6b", "format": 1}, {"name": "tests/integration/targets/helm_set_values/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm_set_values/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "706a0802afe247b21d705adcf9438b038b0d27a20202880fd729aaa46ee4e419", "format": 1}, {"name": "tests/integration/targets/helm_set_values/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/helm_set_values/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "874a80d269db0f7c6297b0eb0749dd569599562f4996aab79c40e5449a9dd19e", "format": 1}, {"name": "tests/integration/targets/helm_set_values/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "1da51e4624ae654400e248308cc2a3c2462be7caefa4420859e2a210146daf78", "format": 1}, {"name": "tests/integration/targets/install_helm", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/install_helm/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/install_helm/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "b4022584c6c1f438eed98fb8ccf09315b6a42a2c2ef6410c8215dc6fffddd778", "format": 1}, {"name": "tests/integration/targets/install_helm/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/install_helm/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "9c3787ff718f388c613ef21205c144de2b7bb193d5acefcb56c67dd9fed0a006", "format": 1}, {"name": "tests/integration/targets/install_helm/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "b5ea375becd3088862c16fc97fe379532c583079829fcf1fdcb549e6808262fb", "format": 1}, {"name": "tests/integration/targets/inventory_k8s", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/inventory_k8s/playbooks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/inventory_k8s/playbooks/vars", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/inventory_k8s/playbooks/vars/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "b7e8c47b08aa790e2ac087169e6bbefbbeecb65892e50165250be616b78155e3", "format": 1}, {"name": "tests/integration/targets/inventory_k8s/playbooks/create_resources.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "6167d41c39b455dd38a1adbef6db6ef0a2462e31d904d0b56f4a1f5e7435a81a", "format": 1}, {"name": "tests/integration/targets/inventory_k8s/playbooks/delete_resources.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "fbcdbc2bb7c3460d47a5bb9951686d97ed9be97050689dfc1a03581ded02abe0", "format": 1}, {"name": "tests/integration/targets/inventory_k8s/playbooks/play.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "9f211af65f7508b30d9d938ceae299bbe40eebdd2f820393b9b10f4c5c94bf3f", "format": 1}, {"name": "tests/integration/targets/inventory_k8s/playbooks/test.inventory_k8s.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "6437d90b9a8f0564bf714d0b4b226fa3a5ba44d057d8f1a693c52624f413f92a", "format": 1}, {"name": "tests/integration/targets/inventory_k8s/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "27896717f6976c7ba215dd050a7699ab34edf3ce1921ec28db8f239a915161e6", "format": 1}, {"name": "tests/integration/targets/inventory_k8s/runme.sh", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "5415aa52dcafe384fbd790cc1af78830c102d68eb355b5cbf757fa0c86a953e7", "format": 1}, {"name": "tests/integration/targets/k8s_access_review", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_access_review/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_access_review/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "aa73c4a87e4a54f6f58904b793357b19e297a1db6625e6a49e5ed24181cde560", "format": 1}, {"name": "tests/integration/targets/k8s_access_review/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "af13a34bbd3fd265aaed6d88eff8518286852e423510ffab27aa1a3c1863469d", "format": 1}, {"name": "tests/integration/targets/k8s_append_hash", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_append_hash/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_append_hash/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "6d53b22bd468c9961454e2a829e3904d3245df0385a92b98ed315d02496d2028", "format": 1}, {"name": "tests/integration/targets/k8s_append_hash/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_append_hash/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "eed0f46a8c4d27944be98eb6ce1d479c161a7e1351fdd874ab355e2d765a3947", "format": 1}, {"name": "tests/integration/targets/k8s_append_hash/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_append_hash/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "8f755c3bc2a38414b1460e2c8d2578f97b53891177d6116181fcd4d8921daad4", "format": 1}, {"name": "tests/integration/targets/k8s_append_hash/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "e41a6e3c02288e649ff00036d6a53272b3ea77651ac003820851aeb6b6a6c14d", "format": 1}, {"name": "tests/integration/targets/k8s_apply", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_apply/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_apply/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "bbb53dbe8d4d2dfa74e23922e85c84bc0d3976328257b389736c393169bad90b", "format": 1}, {"name": "tests/integration/targets/k8s_apply/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_apply/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "eed0f46a8c4d27944be98eb6ce1d479c161a7e1351fdd874ab355e2d765a3947", "format": 1}, {"name": "tests/integration/targets/k8s_apply/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_apply/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "b36ee1d085cbbe29e2aa89a824c4fff99782b24044ad3da3e3d200bbdee2c49b", "format": 1}, {"name": "tests/integration/targets/k8s_apply/tasks/server_side_apply.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "03fb6ca0b70f768f9053e064d1c71fdb6df2bcf951e5452b2fded4b8026e2123", "format": 1}, {"name": "tests/integration/targets/k8s_apply/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "9f23f1e29aff150d3bbceb2294560871dd83c10ca64ce477f778887f3f40bf86", "format": 1}, {"name": "tests/integration/targets/k8s_check_mode", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_check_mode/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_check_mode/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "a455afb6bd96cbde82fb079aa5da2ee22f6438f477c980c4da9c38017e646c85", "format": 1}, {"name": "tests/integration/targets/k8s_check_mode/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_check_mode/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "706a0802afe247b21d705adcf9438b038b0d27a20202880fd729aaa46ee4e419", "format": 1}, {"name": "tests/integration/targets/k8s_check_mode/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_check_mode/tasks/check_mode.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "c885198e0abceb9417edbc9445cd8f431b20c9bb0f92c00c0203801cbed8dcd5", "format": 1}, {"name": "tests/integration/targets/k8s_check_mode/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "1c9af5df8c565794fec8077452ea552469d2e9798e91d58bf86c94182dd3f6fa", "format": 1}, {"name": "tests/integration/targets/k8s_check_mode/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "f3258d0c2a9a2c2d0d72f878e17eb3004a6a8077c2dfdd18f6d7b81b4f7b3241", "format": 1}, {"name": "tests/integration/targets/k8s_cluster_info", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_cluster_info/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_cluster_info/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "5b78e9afdbf59243a6b1b668c12ccc83e368ad7b941af028a2fe8b801e25510e", "format": 1}, {"name": "tests/integration/targets/k8s_cluster_info/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "1bc63533af0fc1dfdb758a018542e3a0413ca8c99f4a57fe9fb6fc21bd5f4da8", "format": 1}, {"name": "tests/integration/targets/k8s_copy", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_copy/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_copy/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "2cd1eed0121342f03ac6c44df0ee5d679a32e773e6d10f5f9e5b086c2599c97c", "format": 1}, {"name": "tests/integration/targets/k8s_copy/files", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_copy/files/data", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_copy/files/data/ansible", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_copy/files/data/ansible/collection.txt", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "ee92d71eac83507748578826b70b4c7627c62fd762d25a5a1b9c6e01fcb3389b", "format": 1}, {"name": "tests/integration/targets/k8s_copy/files/data/ansible/module.txt", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "31f6e65cb2317fe8d9e948ed1b41cfe276513fad87c89dcc7f7dc777e76f3f52", "format": 1}, {"name": "tests/integration/targets/k8s_copy/files/data/teams", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_copy/files/data/teams/ansible.txt", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "cfd95d6ae409c10e16b246437c6c397fe296b3c6ae5259ae6f97e61b8b733e98", "format": 1}, {"name": "tests/integration/targets/k8s_copy/files/data/file.txt", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "6331d720a3a0ffdeed6f17cee165f55167ce05454d11c680105892a5ea5370c7", "format": 1}, {"name": "tests/integration/targets/k8s_copy/files/archive.tar", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "66ea86ef8daa23872007eeeee592167eff42d97bf736d889fb04a863b17d66fd", "format": 1}, {"name": "tests/integration/targets/k8s_copy/files/simple_file.txt", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "91e98678271aa6f939eae03a12d5e211da8ec60504f45b0a8fbd5c834881f2a0", "format": 1}, {"name": "tests/integration/targets/k8s_copy/files/simple_zip_file.txt.gz", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "67c773560130902e8731322c204334a51d43866c81923f1564a9f84f46953b09", "format": 1}, {"name": "tests/integration/targets/k8s_copy/library", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_copy/library/k8s_create_file.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "93f30185ab7c1555eedf7d2a1644459e429098a5a6cd29b655f46e5e986b80d4", "format": 1}, {"name": "tests/integration/targets/k8s_copy/library/kubectl_file_compare.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "b0ac3c26cf1fa994d208e31fddd30dc0df0efc4a8d759e6dc4be8065a743b7fc", "format": 1}, {"name": "tests/integration/targets/k8s_copy/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_copy/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "a3b4b464a995f6bb6c6461efbc9ffc435ce83e9e46767bffeb1c21857b3c51db", "format": 1}, {"name": "tests/integration/targets/k8s_copy/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_copy/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "be8a7eded9036e00f1e0d9cba288079418a203ed5f927cdf7324daf1b17ffc5d", "format": 1}, {"name": "tests/integration/targets/k8s_copy/tasks/test_check_mode.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "bea6efecb7b4575c39c18c506d6afed5d565299cef9fbcd3db6c3885d61a4889", "format": 1}, {"name": "tests/integration/targets/k8s_copy/tasks/test_copy_directory.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "443685ee28f1595bf8a5db62db1493c652a75a2c8c5d7a65facf8f197c841be6", "format": 1}, {"name": "tests/integration/targets/k8s_copy/tasks/test_copy_errors.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "0d880792d8a1bdd6509556506076119bd8ff1ff594e1db87b463bd3e05ce8f64", "format": 1}, {"name": "tests/integration/targets/k8s_copy/tasks/test_copy_file.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "4281ff6283c5eb885d44cc71d0f13bdd87d37bbdb91cda0b776a954725b6f1cf", "format": 1}, {"name": "tests/integration/targets/k8s_copy/tasks/test_copy_item_with_space_in_its_name.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "c27c6d900440350ee03fa8fc686eb892e95e170f62c70ea9aedb3b35b5ff5531", "format": 1}, {"name": "tests/integration/targets/k8s_copy/tasks/test_copy_large_file.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "91ac7bdf37253df75fc425266714bacc2a99a3d69d126429851f816b152a26e2", "format": 1}, {"name": "tests/integration/targets/k8s_copy/tasks/test_multi_container_pod.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "1884a99406cfc2a806e67c74360e7da4dde4cbe2744869a1250008f04e41c006", "format": 1}, {"name": "tests/integration/targets/k8s_copy/templates", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_copy/templates/pods_definition.j2", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "4d03bf465ba7fd8f9412fb793b654fbbcfbb7cba1f8078d03e928238b922f8cb", "format": 1}, {"name": "tests/integration/targets/k8s_copy/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "6c0ba901936063300418a5904f39e93b53d8f14c464f54aa413979dbdd156a08", "format": 1}, {"name": "tests/integration/targets/k8s_crd", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_crd/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_crd/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "a12c194c91806fbf129a9c2409cf7244c53fff7c68e57ec140607abea3c3e6cd", "format": 1}, {"name": "tests/integration/targets/k8s_crd/files", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_crd/files/crd-resource.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "4921362ac3c4afac5f42ebb90b37bcb75e1fe20929bb0e45d0df4c190d28f577", "format": 1}, {"name": "tests/integration/targets/k8s_crd/files/setup-crd.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "509878fff22a19715f1c491930eefd23430c0f571716b463c3ab9a754d0fb250", "format": 1}, {"name": "tests/integration/targets/k8s_crd/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_crd/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "4ebb90920b3deef18b4932f9ec240898d09d173f2e4e2b1248cf77cae5264874", "format": 1}, {"name": "tests/integration/targets/k8s_crd/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_crd/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "6ead0bbf3914807bf845763335db55c924c3a94c1db565b7527a863b303ee430", "format": 1}, {"name": "tests/integration/targets/k8s_crd/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "23c561373551c9f466bf4d6ffb9bc09f4151f45d8ac6fc334efe267e4e6f4765", "format": 1}, {"name": "tests/integration/targets/k8s_delete", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_delete/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_delete/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "a0f8a14d3cc752ecea42c850d8886d616de0a54ec5c458f4683e97368b31959f", "format": 1}, {"name": "tests/integration/targets/k8s_delete/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_delete/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "4ebb90920b3deef18b4932f9ec240898d09d173f2e4e2b1248cf77cae5264874", "format": 1}, {"name": "tests/integration/targets/k8s_delete/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_delete/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "1b24137238beb8cc3cbfb0a6bd54bf7edbc47c888d5386955e62f23d5269d593", "format": 1}, {"name": "tests/integration/targets/k8s_delete/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "2a0bfed7c9118fefdb2369a00609c684eb852aa1a015a7d787125957a0604a9c", "format": 1}, {"name": "tests/integration/targets/k8s_diff", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_diff/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_diff/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "61b856bc0efc6f72a431f5e38f991fcdeb111cc0e82f08e07bf2ef29f9517ca2", "format": 1}, {"name": "tests/integration/targets/k8s_diff/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_diff/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "a5f181f2a4fac30b6ab1513fe8607539e30b4d95d68d7de29a56905844f28998", "format": 1}, {"name": "tests/integration/targets/k8s_diff/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_diff/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "3a199725b66d98cbb7e889ad8e6322268279af39dddb998df20b0807e3582de3", "format": 1}, {"name": "tests/integration/targets/k8s_diff/templates", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_diff/templates/pod.j2", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "3da43f180f8b1af880478c8006f25698bade02f3bff3efe024c3b49b56b1be75", "format": 1}, {"name": "tests/integration/targets/k8s_diff/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "c205fbe9f38acf9d7fc18eb7576971ece1bfdf9e1a47b60963f0dcbca5a44f5b", "format": 1}, {"name": "tests/integration/targets/k8s_drain", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_drain/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_drain/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "6ab9dcab4b9b629bf9c9edb77ecbf75acc0df7a99f7c05170b3ba9cebbcde1d4", "format": 1}, {"name": "tests/integration/targets/k8s_drain/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_drain/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "4ebb90920b3deef18b4932f9ec240898d09d173f2e4e2b1248cf77cae5264874", "format": 1}, {"name": "tests/integration/targets/k8s_drain/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_drain/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "497fcfe822f26afc3b5f3841848ad69bd0e937c6d820833e956f64961d6e6d2f", "format": 1}, {"name": "tests/integration/targets/k8s_drain/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "a31ef70474df899c4c8fd9d794e46af5937059643b057922bcb87593fc11bb86", "format": 1}, {"name": "tests/integration/targets/k8s_exec", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_exec/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_exec/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "aad2b15b929176487ad0f0bcf1bd99d9bd3ab9f0800ecd6205c9f8121a1714ed", "format": 1}, {"name": "tests/integration/targets/k8s_exec/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_exec/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "4ebb90920b3deef18b4932f9ec240898d09d173f2e4e2b1248cf77cae5264874", "format": 1}, {"name": "tests/integration/targets/k8s_exec/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_exec/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "9da1e458f316283c6e6bfd44e182a975dfaba2e89b6f502d201669239c4942ac", "format": 1}, {"name": "tests/integration/targets/k8s_exec/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "a6f83b7741b2c23e92fa0f5d21c9204aee193182a5ab241f894d54f0ee6664e4", "format": 1}, {"name": "tests/integration/targets/k8s_full", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_full/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_full/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "695e74055dc707b2e3aff07a7b0ac58454cce6df4e2156830e8ba6404f8e5274", "format": 1}, {"name": "tests/integration/targets/k8s_full/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_full/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "706a0802afe247b21d705adcf9438b038b0d27a20202880fd729aaa46ee4e419", "format": 1}, {"name": "tests/integration/targets/k8s_full/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_full/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "07c32378aca9d123a4225e6511d744136e4724c17d3dc01411be2f4f85e70d5f", "format": 1}, {"name": "tests/integration/targets/k8s_full/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "472e6e44d7e3935067a0b8f378d9972f3de5fa8eaab569d753972286128c49d3", "format": 1}, {"name": "tests/integration/targets/k8s_gc", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_gc/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_gc/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "3b7da29b5dae95612c694eb918cb3b56a3f1f2cff323622903f0bc2f700e40aa", "format": 1}, {"name": "tests/integration/targets/k8s_gc/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_gc/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "4ebb90920b3deef18b4932f9ec240898d09d173f2e4e2b1248cf77cae5264874", "format": 1}, {"name": "tests/integration/targets/k8s_gc/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_gc/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "c934fafe64f035864ab730f94a881c1a7da97623f307c9b48897907e7524e213", "format": 1}, {"name": "tests/integration/targets/k8s_gc/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "1f1e71ef536c418b80b3c89bb739c762c5a52cf5533e80aa286cd5ff3e168cb1", "format": 1}, {"name": "tests/integration/targets/k8s_generate_name", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_generate_name/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_generate_name/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "55c28b0a6d904883e95e8bb012ab64942e3ddbe0377d36843fc7a73815e4aa82", "format": 1}, {"name": "tests/integration/targets/k8s_generate_name/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "412084ec88b9ab491edd7114db00d72f0e139d10ac3d31e08045e0a5478b2366", "format": 1}, {"name": "tests/integration/targets/k8s_info", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_info/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_info/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "9b329b4edb06334a5cbe4b1118799ca0592a177e43e040336a68fa27ec409fe1", "format": 1}, {"name": "tests/integration/targets/k8s_info/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_info/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "4ebb90920b3deef18b4932f9ec240898d09d173f2e4e2b1248cf77cae5264874", "format": 1}, {"name": "tests/integration/targets/k8s_info/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_info/tasks/api-server-caching.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "70f2353923430f02fc3d214a3f1adbcd1ed61596c658074890ce3e3749cc720d", "format": 1}, {"name": "tests/integration/targets/k8s_info/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "f4b0280dda29909d09c166ef0a976a405593b8536cc158ea6bf75d4841369dc7", "format": 1}, {"name": "tests/integration/targets/k8s_info/tasks/wait.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "396d2ee6262c763600d91b36df4815a8d7c4981337ddf4fedf0f93c61eaf6c09", "format": 1}, {"name": "tests/integration/targets/k8s_info/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "8ed78ec8cbde6a6dfe3dec44b004648c8215c0861e26a84e145378c9b38149cf", "format": 1}, {"name": "tests/integration/targets/k8s_json_patch", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_json_patch/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_json_patch/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "524da1ad18f4157306dd9cdf21cf680295a422848f41022a344827f2b26b5c28", "format": 1}, {"name": "tests/integration/targets/k8s_json_patch/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_json_patch/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "4ebb90920b3deef18b4932f9ec240898d09d173f2e4e2b1248cf77cae5264874", "format": 1}, {"name": "tests/integration/targets/k8s_json_patch/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_json_patch/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "64961ac3f927d5f12a1033656910abd24d1c3d680fd6b02e774fb320a114c833", "format": 1}, {"name": "tests/integration/targets/k8s_json_patch/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "22be02d0683dc05d3d4b3595a568e93433c80be1bad902c40f9db6205047143e", "format": 1}, {"name": "tests/integration/targets/k8s_label_selectors", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_label_selectors/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_label_selectors/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "9436e442e65820931406d29f4d87a0ebd3d42cd9b9d1cff92c475caa573af9c1", "format": 1}, {"name": "tests/integration/targets/k8s_label_selectors/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_label_selectors/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "4ebb90920b3deef18b4932f9ec240898d09d173f2e4e2b1248cf77cae5264874", "format": 1}, {"name": "tests/integration/targets/k8s_label_selectors/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_label_selectors/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "e7762bcc85bd00416a723b2774bedd61d569e36bf1ecd53296ce696d6e439b5b", "format": 1}, {"name": "tests/integration/targets/k8s_label_selectors/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "654de33efd4ce3d3b4022bda5bf2f368f7710040b634e7862e3743fdf320e79f", "format": 1}, {"name": "tests/integration/targets/k8s_lists", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_lists/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_lists/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "585b19e6e2307bac28211a83a843e46587e4bb64434922ab1bfd46e71817334a", "format": 1}, {"name": "tests/integration/targets/k8s_lists/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_lists/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "4ebb90920b3deef18b4932f9ec240898d09d173f2e4e2b1248cf77cae5264874", "format": 1}, {"name": "tests/integration/targets/k8s_lists/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_lists/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "5b2de720b3dc5adc96ea2de9f56aeb7c671eb5717d60c04f3dabdb397219ff16", "format": 1}, {"name": "tests/integration/targets/k8s_lists/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "dc5cd39b9881b9b8492b587b76b93403d7d715d45fab3b12d4475e6ef591c678", "format": 1}, {"name": "tests/integration/targets/k8s_log", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_log/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_log/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "98be34ec13b636ae16821e0fce1badbd91780188a2ac5392099c9cde89a53ea3", "format": 1}, {"name": "tests/integration/targets/k8s_log/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_log/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "4ebb90920b3deef18b4932f9ec240898d09d173f2e4e2b1248cf77cae5264874", "format": 1}, {"name": "tests/integration/targets/k8s_log/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_log/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "22a4a75d97f3d7551f2b86c4d2178640357fa575ddd0ce980fa2d545e042842c", "format": 1}, {"name": "tests/integration/targets/k8s_log/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "9d3f34076ef04de7f3c32b6a2240b8b9da7456f3e9d7a95badfef14f88339087", "format": 1}, {"name": "tests/integration/targets/k8s_manifest_url", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_manifest_url/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_manifest_url/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "dfa5fc684930a27110a4c60eca6821a3102b9fd06e0573a139c5353ee3e3599b", "format": 1}, {"name": "tests/integration/targets/k8s_manifest_url/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_manifest_url/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "4ebb90920b3deef18b4932f9ec240898d09d173f2e4e2b1248cf77cae5264874", "format": 1}, {"name": "tests/integration/targets/k8s_manifest_url/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_manifest_url/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "da9b6909348a797d59e02f1e612e58f3ae54e2d597e264aed30482734c55094d", "format": 1}, {"name": "tests/integration/targets/k8s_manifest_url/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "77e2a017cb4c6bbb4c4010a92cdee2e52c26e15671c521d501d1d7823a5374fc", "format": 1}, {"name": "tests/integration/targets/k8s_merge_type", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_merge_type/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_merge_type/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "b8b822783619e55604574f022da0e6ed37834bf4dd5e0f23141d71130e1a8bd5", "format": 1}, {"name": "tests/integration/targets/k8s_merge_type/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_merge_type/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "a5f181f2a4fac30b6ab1513fe8607539e30b4d95d68d7de29a56905844f28998", "format": 1}, {"name": "tests/integration/targets/k8s_merge_type/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_merge_type/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "34bd00a6aaaa4ab66781553e4e255c09bd5e713a2edef845dcc9c158a9a025e8", "format": 1}, {"name": "tests/integration/targets/k8s_merge_type/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "45a2243f71e6555a61f5019887d5b8c89f8bc502b751820863e461efaeb98404", "format": 1}, {"name": "tests/integration/targets/k8s_patched", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_patched/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_patched/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "2d6984fc4d87dec00389da9bf9c7ef3d474f70326a7f44a622ba1cadc90f50ec", "format": 1}, {"name": "tests/integration/targets/k8s_patched/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_patched/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "2fae65508a87db152a9cfbe2f9265c6ab3e316fe0c8f99d63ec88881a06bd47d", "format": 1}, {"name": "tests/integration/targets/k8s_patched/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_patched/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "255128481c38ce5a0e0c53047a5b111156aba224d31f9bd395408f6d02e561f1", "format": 1}, {"name": "tests/integration/targets/k8s_patched/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "8952ed443607c851edaf37e4ef3f150fab1d3180cdbdb6d7833d270a06e3a3cd", "format": 1}, {"name": "tests/integration/targets/k8s_rollback", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_rollback/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_rollback/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "86a1692a3383c8df8a72ea9b1fcbe6ef0153ed0c792f2b365ab97d832e61ee80", "format": 1}, {"name": "tests/integration/targets/k8s_rollback/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_rollback/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "a5f181f2a4fac30b6ab1513fe8607539e30b4d95d68d7de29a56905844f28998", "format": 1}, {"name": "tests/integration/targets/k8s_rollback/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_rollback/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "20843a31b17b7fd10c7e55ceddfdc1a0f8da8d928a05cd138ccf24a6dea0128e", "format": 1}, {"name": "tests/integration/targets/k8s_rollback/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "df87fd2e6b8022198931711f0d0356f940057ea3f900b5b15d3543f837480ee5", "format": 1}, {"name": "tests/integration/targets/k8s_scale", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_scale/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_scale/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "bc5a7447c9af668aa3c070711ae0d04aac73126d2606b9494d170f3a54331ba0", "format": 1}, {"name": "tests/integration/targets/k8s_scale/files", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_scale/files/deployment.yaml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "7aace180b64cf64511b9d3f32d42e177e8f7dac0c7c752ac104852a20146c332", "format": 1}, {"name": "tests/integration/targets/k8s_scale/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_scale/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "a5f181f2a4fac30b6ab1513fe8607539e30b4d95d68d7de29a56905844f28998", "format": 1}, {"name": "tests/integration/targets/k8s_scale/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_scale/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "af632f359f1507bfc0a8e4eb1b2b36e1222a06ae21859d7c9dfc83da19a37118", "format": 1}, {"name": "tests/integration/targets/k8s_scale/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "06781a75febe39f510b5228928025aed8268cb9e10f0c54d5fd9cb3b1fdb416b", "format": 1}, {"name": "tests/integration/targets/k8s_taint", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_taint/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_taint/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "1e7c37e4067db67fe54f46551fe12d845a9e42bf640942cc01b37f9977532e6e", "format": 1}, {"name": "tests/integration/targets/k8s_taint/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_taint/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "a5f181f2a4fac30b6ab1513fe8607539e30b4d95d68d7de29a56905844f28998", "format": 1}, {"name": "tests/integration/targets/k8s_taint/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_taint/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "91e0d8bf83a03733a3d875d635a15d2788238c3371d30abdcb97005cc9152c10", "format": 1}, {"name": "tests/integration/targets/k8s_taint/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "a1cf831f5a94b5f55971cadc974d6f9d61f8260a66f5a0ebd9b55922ff3f6de9", "format": 1}, {"name": "tests/integration/targets/k8s_template", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_template/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_template/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "175b6fd740625da1725068a851364a84b6ece070beb0d5a9f22933dd9d765df8", "format": 1}, {"name": "tests/integration/targets/k8s_template/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_template/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "a5f181f2a4fac30b6ab1513fe8607539e30b4d95d68d7de29a56905844f28998", "format": 1}, {"name": "tests/integration/targets/k8s_template/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_template/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "bf3597ac3979f659fdd8ebdb35d9851238eff3cea0333f85a4105ec19a03cf42", "format": 1}, {"name": "tests/integration/targets/k8s_template/templates", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_template/templates/configmap.yml.j2", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "cd827ce6e8e1d83551da796b8114d56c3b631d8af5dbec27e649b4e5da43978c", "format": 1}, {"name": "tests/integration/targets/k8s_template/templates/pod_one.j2", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "f406fafaee09dfaa626acfa30bdca5a211e3a23c5ce9bdbd709dfad382636e9f", "format": 1}, {"name": "tests/integration/targets/k8s_template/templates/pod_three.j2", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "a9260813cfbc5f9c354da71c35c7bfb4a0bb3854448bd3172a5c672ca58cc827", "format": 1}, {"name": "tests/integration/targets/k8s_template/templates/pod_two.j2", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "41da088ff2b14211959cade261eeb291e7b4aea00ff3a3deb76b1595d795a221", "format": 1}, {"name": "tests/integration/targets/k8s_template/templates/pod_with_bad_namespace.j2", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "2d47b2e060a0ffe2fb0ec36f9e9fae51a2d3bfed0a1527def18cb4ce7a9d5a0c", "format": 1}, {"name": "tests/integration/targets/k8s_template/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "db26c789a46be23ef24852943678fe46f7f48a774055c8e3ba6dc826952d073d", "format": 1}, {"name": "tests/integration/targets/k8s_user_impersonation", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_user_impersonation/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_user_impersonation/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "ec5086725790883056015ccc7ac65e8e46eb2db662632b387edbf9c6d95374b7", "format": 1}, {"name": "tests/integration/targets/k8s_user_impersonation/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_user_impersonation/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "a5f181f2a4fac30b6ab1513fe8607539e30b4d95d68d7de29a56905844f28998", "format": 1}, {"name": "tests/integration/targets/k8s_user_impersonation/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_user_impersonation/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "378799cbb98a365b7559e9f6ff9a8ed6cf2abcff97d4ea47a5cd221f795cf12b", "format": 1}, {"name": "tests/integration/targets/k8s_user_impersonation/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "677cabf2fbc556f8d5a4e36908af1056de9790c9805b8b75a5a66cca66b5cdfa", "format": 1}, {"name": "tests/integration/targets/k8s_validate", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_validate/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_validate/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "36c6277f8c8b62d2f53390afb0305c484f9b8c7496aeba894dd5e6739f0334a3", "format": 1}, {"name": "tests/integration/targets/k8s_validate/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_validate/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "a5f181f2a4fac30b6ab1513fe8607539e30b4d95d68d7de29a56905844f28998", "format": 1}, {"name": "tests/integration/targets/k8s_validate/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_validate/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "6cbec7acc2026e44531f73245f00878f03a52bd4cb0c774335c628a421cb9f28", "format": 1}, {"name": "tests/integration/targets/k8s_validate/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "6fa0cf6146c6cf230ad5a94dcd9b8a431ea32ff4949f95305c2fa5f42a322cf2", "format": 1}, {"name": "tests/integration/targets/k8s_waiter", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_waiter/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_waiter/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "0132e1ae45ede9940d047ecadb7b81abe95b87f0d0e84a4b79b4db4a95659179", "format": 1}, {"name": "tests/integration/targets/k8s_waiter/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_waiter/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "a5f181f2a4fac30b6ab1513fe8607539e30b4d95d68d7de29a56905844f28998", "format": 1}, {"name": "tests/integration/targets/k8s_waiter/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/k8s_waiter/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "f5a9e3908edbba802a64be408d037cd4bf60bc790b9c0292ce20d8affd7832ac", "format": 1}, {"name": "tests/integration/targets/k8s_waiter/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "ca396e8f0b136c71381c48a4b8d524bde59563ce74651a5dd5ede0b0a387852c", "format": 1}, {"name": "tests/integration/targets/lookup_k8s", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/lookup_k8s/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/lookup_k8s/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "3fcae450da9465d6b5dc2d5f89a6aa2a7141250ff66eb19656be5047ac273801", "format": 1}, {"name": "tests/integration/targets/lookup_k8s/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/lookup_k8s/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "2fae65508a87db152a9cfbe2f9265c6ab3e316fe0c8f99d63ec88881a06bd47d", "format": 1}, {"name": "tests/integration/targets/lookup_k8s/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/lookup_k8s/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "9a868413c3830277cdb94c9d8bc2df9a330e44e304b90687953112e677b8b03c", "format": 1}, {"name": "tests/integration/targets/lookup_k8s/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "02e8e6384636b111fe80a3ed7d95ae88b45e43f441fe85398e03fff177d24a5d", "format": 1}, {"name": "tests/integration/targets/lookup_kustomize", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/lookup_kustomize/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/lookup_kustomize/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "0c969831fe1c4a67d534489b3fe52560495b3a45498933f8885bca4ed0c0a336", "format": 1}, {"name": "tests/integration/targets/lookup_kustomize/meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/lookup_kustomize/meta/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "a5f181f2a4fac30b6ab1513fe8607539e30b4d95d68d7de29a56905844f28998", "format": 1}, {"name": "tests/integration/targets/lookup_kustomize/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/lookup_kustomize/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "0a20b1f483f8fb3d48c6e39c6adb6e122cc0ef44dd826aceaa55b269553f7a54", "format": 1}, {"name": "tests/integration/targets/lookup_kustomize/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "f63699f10511a8f34dc30149c03cbff800713418d9be0a5ff4192dff5a614e5e", "format": 1}, {"name": "tests/integration/targets/remove_namespace", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/remove_namespace/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/remove_namespace/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "35bf3578a016147b01d233717f5c031a2f2bbcc24385a89ef355289a08cda2ea", "format": 1}, {"name": "tests/integration/targets/remove_namespace/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "b5ea375becd3088862c16fc97fe379532c583079829fcf1fdcb549e6808262fb", "format": 1}, {"name": "tests/integration/targets/setup_kubeconfig", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/setup_kubeconfig/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/setup_kubeconfig/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "3524e085b6d13b400bb15c8b52c45869348fceba03ed61c1ab1bb05396a4ebe1", "format": 1}, {"name": "tests/integration/targets/setup_kubeconfig/library", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/setup_kubeconfig/library/test_inventory_read_credentials.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "50fa9ab8aca36af544a370d3c2b5d3b557bd848d0d9ae1f0e751634ea75bbb6b", "format": 1}, {"name": "tests/integration/targets/setup_kubeconfig/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/setup_kubeconfig/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "3a0eaae728b0f81b1ca7b308fe0b89c5985cddd0de4127dc113ef8c3edd8b14e", "format": 1}, {"name": "tests/integration/targets/setup_kubeconfig/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "b5ea375becd3088862c16fc97fe379532c583079829fcf1fdcb549e6808262fb", "format": 1}, {"name": "tests/integration/targets/setup_namespace", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/setup_namespace/defaults", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/setup_namespace/defaults/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "f52d711103d50a437830c6fbcd04fb4bab49a0f82f6d26d1c791c6e8488dd090", "format": 1}, {"name": "tests/integration/targets/setup_namespace/tasks", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/integration/targets/setup_namespace/tasks/create.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "adf69a27e38631719bd163dc0181e685fe769148a82f1319cd24f0486c7d2541", "format": 1}, {"name": "tests/integration/targets/setup_namespace/tasks/main.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "17cc7f98717af53c169f83f5beb784c023b02c0a1c82ce626d63557362826c98", "format": 1}, {"name": "tests/integration/targets/setup_namespace/aliases", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "b5ea375becd3088862c16fc97fe379532c583079829fcf1fdcb549e6808262fb", "format": 1}, {"name": "tests/sanity", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/sanity/ignore-2.10.txt", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "df0567067fd935ff36eae1b6952a83b392be1c333519821a3af56437f4a55a2f", "format": 1}, {"name": "tests/sanity/ignore-2.11.txt", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "70624e6d15f87ec118ba71c135b060431f04aa3746123666e8cb951b9cb1bca1", "format": 1}, {"name": "tests/sanity/ignore-2.12.txt", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "c108894b090a50da8f307726409f3055b481d5ee255f2932c1d52bae72d50058", "format": 1}, {"name": "tests/sanity/ignore-2.13.txt", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "c108894b090a50da8f307726409f3055b481d5ee255f2932c1d52bae72d50058", "format": 1}, {"name": "tests/sanity/ignore-2.14.txt", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "c108894b090a50da8f307726409f3055b481d5ee255f2932c1d52bae72d50058", "format": 1}, {"name": "tests/sanity/ignore-2.15.txt", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "459348e18aabc4f5a164df89f028a7c736551ad166e14e24924a209cab0e52bf", "format": 1}, {"name": "tests/sanity/ignore-2.9.txt", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "f81ba55b45ea34f4fe65ecaba048fb917867bd7b87b020069ddb83f145179391", "format": 1}, {"name": "tests/sanity/refresh_ignore_files", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "f4caf94f5cdc9e95d6761febbbd1a5b0632777c5fe1baf12e070c172da887d65", "format": 1}, {"name": "tests/unit", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/unit/action", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/unit/action/test_remove_omit.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "30e4e2521f84b3bd3a408ebb75c3adeeb0e99382bd9f34afc461ae6778fa5683", "format": 1}, {"name": "tests/unit/module_utils", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/unit/module_utils/fixtures", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/unit/module_utils/fixtures/definitions.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "a800f11cdae06784834a41a93a60f866320ffe44a6047b11ce98ce528ce3fabe", "format": 1}, {"name": "tests/unit/module_utils/fixtures/deployments.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "6033cce016f1619d27553a452bcd66c43ea83cb78e0b34bd63881dd0888b97df", "format": 1}, {"name": "tests/unit/module_utils/fixtures/pods.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "984527710b247eae4b144baf9bcd1068d6c481ea9492a3031a316a10d40bfa51", "format": 1}, {"name": "tests/unit/module_utils/test_apply.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "28703ed0cab23068fbbd0d99ba1095ca2c2c484f28fa25c05716ed29671c2ac5", "format": 1}, {"name": "tests/unit/module_utils/test_client.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "84829637b1d0d7210f75b3c33210dde9d1233230563b9fcf0557695787e9f700", "format": 1}, {"name": "tests/unit/module_utils/test_common.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "de8c6088bf3f034ea167b86cb22adea982c4bd1320d0eccd3771b053f9481a05", "format": 1}, {"name": "tests/unit/module_utils/test_core.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "25d001234f6af4441e1fd3e75b9a691f0b016662000e898d7603879ec01eb57e", "format": 1}, {"name": "tests/unit/module_utils/test_discoverer.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "0517cb0710d18a3730e675e6206e21fc50ec98c6338eb25547838d4cf107dd87", "format": 1}, {"name": "tests/unit/module_utils/test_hashes.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "3d01aed53815b18dc89fd1373975261e5a36f99ef2fba9a86e28406bc103309c", "format": 1}, {"name": "tests/unit/module_utils/test_helm.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "e7b1d5eec28c07a73b0e71b67bf6211496adb11c9ff748f7bb47e7c5bc279ed4", "format": 1}, {"name": "tests/unit/module_utils/test_marshal.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "3aa6c8559019f59af3518fcb63c92feb4bbd115ae052d35e0569e992421508ac", "format": 1}, {"name": "tests/unit/module_utils/test_resource.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "add99dd95ed20a3352d53eb1357fb84fd38a6ecacc2cce4674172840dd062209", "format": 1}, {"name": "tests/unit/module_utils/test_runner.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "bf805b42bed1e709ce7d2b2bb2139d0b9014ba3a712168b0095f78f4143217e4", "format": 1}, {"name": "tests/unit/module_utils/test_selector.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "67deb919d8c08d09b06a4926fe0460ef98404eb833cb11ffa0d16a77c0f8bda6", "format": 1}, {"name": "tests/unit/module_utils/test_service.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "22eb9a8b500a62548486b754dacccd40f92e612659705cda953c68e95178a802", "format": 1}, {"name": "tests/unit/module_utils/test_waiter.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "a83e8cf7a00c1f6f6d9b08fcb01a9b0807f3d59081c4697da0bec1908a1a6eb1", "format": 1}, {"name": "tests/unit/modules", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/unit/modules/test_helm_template.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "add6e070d2495e7762db03cd967ea526c3b722f7cd9e0d91ecf682f734600912", "format": 1}, {"name": "tests/unit/modules/test_helm_template_module.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "ce4462f8dc0e6f61abd6ed59951d95ed57aac564da92b494d23d31c963ce5df6", "format": 1}, {"name": "tests/unit/modules/test_module_helm.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "65480cc9a747d2f5d7f14e9936f9c53ca4cd781b1e01c9d739304795d0c8d259", "format": 1}, {"name": "tests/unit/utils", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/unit/utils/ansible_module_mock.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "26336dbd51c1c5644abb4bcc51ffc18f4b1c03c71ac0a9430b2135edf8132832", "format": 1}, {"name": "tests/unit/conftest.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "e71c6426972ffd4826b7d5fb7f021333ca0760ea442b9e549b0916e837cd54c7", "format": 1}, {"name": "tests/unit/requirements.txt", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "9786cc70a5b99e738917f0b25684cf4caa752c01d7c298be0d3526af282a0346", "format": 1}, {"name": "tests/config.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "9a009a349eaaf78c93ff56072d2ef171937bdb884e4976592ab5aaa9c68e1044", "format": 1}, {"name": ".giti<PERSON>re", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "5d56adf26b6ed553559348325b50747c3b7d664cdd59982cb438016cbe8ce75c", "format": 1}, {"name": ".yam<PERSON>t", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "3502ec6ac2192fb928af5c4e85df274d577553d4eb533f6f37c0957953cbec2a", "format": 1}, {"name": "CHANGELOG.rst", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "e7d76b349ab603ebc12c9b49cf98017e88a9b8ba1bda3a26a00aa664b9fb2594", "format": 1}, {"name": "CONTRIBUTING.md", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "5a96179224855c796561d451fd8eb4430a8ec054ef34ad3201423b8e7fa4c75f", "format": 1}, {"name": "LICENSE", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "8b1ba204bb69a0ade2bfcf65ef294a920f6bb361b317dba43c7ef29d96332b9b", "format": 1}, {"name": "<PERSON><PERSON><PERSON>", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "bec33eed95fe1d63e3fb0a9085318d6916c24eb6de43f0c34ed9dc4e3b678c50", "format": 1}, {"name": "PSF-license.txt", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "83b042fc7d6aca0f10d68e45efa56b9bc0a1496608e7e7728fe09d1a534a054a", "format": 1}, {"name": "README.md", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "96e8d2d129338de73a45a8957e505822a152b8265c870fb6a92cd6e35bb4e30d", "format": 1}, {"name": "bindep.txt", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "488be8bac2e9a5d7b4518cc1cbc71f1cf5bd7e080da852f673c45b4f68f6467a", "format": 1}, {"name": "codecov.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "caa848a2e02be5014890c5cbc7727e9a00d40394637c90886eb813d60f82c9c3", "format": 1}, {"name": "requirements.txt", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "f4d1053cac2a14673a9faf001292589693d10f6126b23255e34beb8223d1de91", "format": 1}, {"name": "setup.cfg", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "91352cd0180469f3b0114c839c6d1df890fa8599c411346f30461a7e4490fbfd", "format": 1}, {"name": "test-requirements.txt", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "5f2d972c940f041edc9dd2affe30bf4dd1543e35a471125dafce1f5b64026767", "format": 1}, {"name": "tox.ini", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "63b3c9e7c1e29e051dde455446312eaededeee7006c4e5e23746049fa7de2255", "format": 1}], "format": 1}