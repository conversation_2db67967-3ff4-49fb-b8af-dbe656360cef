---
kind: Namespace
apiVersion: v1
metadata:
  name: test-1
---
kind: Pod
apiVersion: v1
metadata:
  name: pod-1
  namespace: test-1
spec:
  containers:
    - image: busybox
      name: busybox
---
kind: PodList
apiVersion: v1
metadata: {}
items:
  - kind: Pod
    apiVersion: v1
    metadata:
      name: pod-1
      namespace: test-1
    spec:
      containers:
        - image: busybox
          name: busybox
---
kind: ConfigMapList
apiVersion: v1
metadata: {}
items: []
