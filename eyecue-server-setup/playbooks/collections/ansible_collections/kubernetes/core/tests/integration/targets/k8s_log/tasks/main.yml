---
- block:
    - name: Retrieve log from unexisting Pod
      k8s_log:
        namespace: "{{ test_namespace }}"
        name: "this_pod_does_exist"
      ignore_errors: true
      register: fake_pod

    - name: Assert that task failed with proper message
      assert:
        that:
          - fake_pod is failed
          - 'fake_pod.msg == "Pod {{ test_namespace }}/this_pod_does_exist not found."'

    - name: create hello-world deployment
      k8s:
        wait: yes
        wait_timeout: "{{ k8s_wait_timeout | default(omit) }}"
        definition:
          apiVersion: apps/v1
          kind: Deployment
          metadata:
            name: hello-world
            namespace: "{{ test_namespace }}"
          spec:
            selector:
              matchLabels:
                app: hello-world
            template:
              metadata:
                labels:
                  app: hello-world
              spec:
                containers:
                  - image: busybox
                    name: hello-world
                    command: ['sh']
                    args: ['-c', 'while true ; do echo "hello world" && sleep 10 ; done']
                restartPolicy: Always

    - name: retrieve the log by providing the deployment
      k8s_log:
        api_version: apps/v1
        kind: Deployment
        namespace: "{{ test_namespace }}"
        name: hello-world
      register: deployment_log

    - name: verify that the log can be retrieved via the deployment
      assert:
        that:
          - "'hello world' in deployment_log.log"
          - item == 'hello world' or item == ''
      with_items: '{{ deployment_log.log_lines }}'

    - name: retrieve the log with a label selector
      k8s_log:
        namespace: "{{ test_namespace }}"
        label_selectors:
          - 'app=hello-world'
      register: label_selector_log

    - name: verify that the log can be retrieved via the label
      assert:
        that:
          - "'hello world' in label_selector_log.log"
          - item == 'hello world' or item == ''
      with_items: '{{ label_selector_log.log_lines }}'

    - name: get the hello-world pod
      k8s_info:
        kind: Pod
        namespace: "{{ test_namespace }}"
        label_selectors:
          - 'app=hello-world'
      register: k8s_log_pods

    - name: retrieve the log directly with the pod name
      k8s_log:
        namespace: "{{ test_namespace }}"
        name: '{{ k8s_log_pods.resources.0.metadata.name }}'
      register: pod_log

    - name: verify that the log can be retrieved via the pod name
      assert:
        that:
          - "'hello world' in pod_log.log"
          - item == 'hello world' or item == ''
      with_items: '{{ pod_log.log_lines }}'

    - name: Create a job that calculates 7
      k8s:
        state: present
        wait: yes
        wait_timeout: "{{ k8s_wait_timeout | default(omit) }}"
        wait_condition:
          type: Complete
          status: 'True'
        definition:
          apiVersion: batch/v1
          kind: Job
          metadata:
            name: int-log
            namespace: "{{ test_namespace }}"
          spec:
            template:
              spec:
                containers:
                  - name: busybox
                    image: busybox
                    command: ["echo", "7"]
                restartPolicy: Never
            backoffLimit: 4

    - name: retrieve logs from the job
      k8s_log:
        api_version: batch/v1
        kind: Job
        namespace: "{{ test_namespace }}"
        name: int-log
      register: job_logs

    - name: verify the log was successfully retrieved
      assert:
        that: job_logs.log_lines[0] == "7"

    - name: create a job that has 10 log lines
      k8s:
        state: present
        wait: yes
        wait_timeout: "{{ k8s_wait_timeout | default(omit) }}"
        wait_condition:
          type: Complete
          status: 'True'
        definition:
          apiVersion: batch/v1
          kind: Job
          metadata:
            name: multiline-log
            namespace: "{{ test_namespace }}"
          spec:
            template:
              spec:
                containers:
                  - name: busybox
                    image: busybox
                    command: ['sh']
                    args: ['-c', 'for i in $(seq 0 9); do echo $i; done']
                restartPolicy: Never
            backoffLimit: 4

    - name: retrieve last 5 log lines from the job
      k8s_log:
        api_version: batch/v1
        kind: Job
        namespace: "{{ test_namespace }}"
        name: multiline-log
        tail_lines: 5
      register: tailed_log

    # The log_lines by k8s_log always contain a trailing empty element,
    # so if "tail"ing 5 lines, the length will be 6.
    - name: verify that specific number of logs have been retrieved
      assert:
        that: tailed_log.log_lines | length == 5 + 1

    # Trying to call module without name and label_selectors
    - name: Retrieve without neither name nor label_selectors provided
      k8s_log:
        namespace: "{{ test_namespace }}"
      register: noname_log
      ignore_errors: true

    - name: Ensure task failed
      assert:
        that:
          - noname_log is failed
          - 'noname_log.msg == "name must be provided for resources that do not support namespaced base url"'

    # Test retrieve all containers logs
    - name: Create deployments
      k8s:
        namespace: "{{ test_namespace }}"
        wait: yes
        wait_timeout: "{{ k8s_wait_timeout | default(omit) }}"
        wait_condition:
          type: Complete
          status: 'True'
        definition:
          apiVersion: batch/v1
          kind: Job
          metadata:
            name: multicontainer-log
          spec:
            template:
              spec:
                containers:
                  - name: p01
                    image: busybox
                    command: ['sh']
                    args: ['-c', 'for i in $(seq 0 9); do echo $i; done']
                  - name: p02
                    image: busybox
                    command: ['sh']
                    args: ['-c', 'for i in $(seq 10 19); do echo $i; done']
                restartPolicy: Never

    - name: Retrieve logs from all containers
      k8s_log:
        api_version: batch/v1
        kind: Job
        namespace: "{{ test_namespace }}"
        name: multicontainer-log
        all_containers: true
      register: all_logs

    - name: Retrieve logs from first job
      k8s_log:
        api_version: batch/v1
        kind: Job
        namespace: "{{ test_namespace }}"
        name: multicontainer-log
        container: p01
      register: log_1

    - name: Retrieve logs from second job
      k8s_log:
        api_version: batch/v1
        kind: Job
        namespace: "{{ test_namespace }}"
        name: multicontainer-log
        container: p02
      register: log_2

    - name: Validate that log using all_containers=true is the sum of all logs
      assert:
        that:
          - all_logs.log == (log_1.log + log_2.log)

  always:
    - name: ensure that namespace is removed
      k8s:
        kind: Namespace
        name: "{{ test_namespace }}"
        state: absent
      ignore_errors: true
