---
- block:
    - set_fact:
        patch_only_namespace: "{{ test_namespace }}"

    - name: Ensure namespace {{ patch_only_namespace[0] }} exist
      kubernetes.core.k8s:
        definition:
          apiVersion: v1
          kind: Namespace
          metadata:
            name: "{{ patch_only_namespace[0] }}"
            labels:
              existingLabel: "labelValue"
            annotations:
              existingAnnotation: "annotationValue"
        wait: yes

    - name: Ensure namespace {{ patch_only_namespace[1] }} does not exist
      kubernetes.core.k8s_info:
        kind: namespace
        name: "{{ patch_only_namespace[1] }}"
      register: second_namespace

    - name: assert that second namespace does not exist
      assert:
        that:
          - second_namespace.resources | length == 0

    - name: apply patch on existing resource
      kubernetes.core.k8s:
        state: patched
        wait: yes
        definition: |
          ---
          apiVersion: v1
          kind: Namespace
          metadata:
            name: "{{ patch_only_namespace[0] }}"
            labels:
              ansible: patched
          ---
          apiVersion: v1
          kind: Namespace
          metadata:
            name: "{{ patch_only_namespace[1] }}"
            labels:
              ansible: patched
      register: patch_resource

    - name: assert that patch succeed
      assert:
        that:
          - patch_resource.changed
          - patch_resource.result.results | selectattr('warnings', 'defined') | list | length == 1

    - name: Ensure namespace {{ patch_only_namespace[0] }} was patched correctly
      kubernetes.core.k8s_info:
        kind: namespace
        name: "{{ patch_only_namespace[0] }}"
      register: first_namespace

    - name: assert labels are as expected
      assert:
        that:
          - first_namespace.resources[0].metadata.labels.ansible == "patched"
          - first_namespace.resources[0].metadata.labels.existingLabel == "labelValue"
          - first_namespace.resources[0].metadata.annotations.existingAnnotation == "annotationValue"
    - name: Ensure namespace {{ patch_only_namespace[1] }} was not created
      kubernetes.core.k8s_info:
        kind: namespace
        name: "{{ patch_only_namespace[1] }}"
      register: second_namespace

    - name: assert that second namespace does not exist
      assert:
        that:
          - second_namespace.resources | length == 0

    - name: patch all resources (create if does not exist)
      kubernetes.core.k8s:
        state: present
        definition: |
          ---
          apiVersion: v1
          kind: Namespace
          metadata:
            name: "{{ patch_only_namespace[0] }}"
            labels:
              patch: ansible
          ---
          apiVersion: v1
          kind: Namespace
          metadata:
            name: "{{ patch_only_namespace[1] }}"
            labels:
              patch: ansible
        wait: yes
      register: patch_resource

    - name: Ensure namespace {{ patch_only_namespace[1] }} was created
      kubernetes.core.k8s_info:
        kind: namespace
        name: "{{ patch_only_namespace[1] }}"
      register: second_namespace

    - name: assert that second namespace exist
      assert:
        that:
          - second_namespace.resources | length == 1

  always:
    - name: Remove namespace
      kubernetes.core.k8s:
        kind: Namespace
        name: "{{ item }}"
        state: absent
      with_items:
        - "{{ patch_only_namespace[0] }}"
        - "{{ patch_only_namespace[1] }}"
      ignore_errors: true
