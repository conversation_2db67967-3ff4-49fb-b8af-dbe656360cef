---
- block:
  - name: copy directory into remote Pod (create new directory)
    k8s_cp:
      namespace: '{{ copy_namespace }}'
      pod: '{{ pod_with_one_container.name }}'
      remote_path: /dest_data
      local_path: files/data
      state: to_pod

  - name: compare directories
    kubectl_file_compare:
      namespace: '{{ copy_namespace }}'
      pod: '{{ pod_with_one_container.name }}'
      remote_path: /dest_data
      local_path: '{{ role_path }}/files/data'

  - name: copy directory into remote Pod (existing directory)
    k8s_cp:
      namespace: '{{ copy_namespace }}'
      pod: '{{ pod_with_one_container.name }}'
      remote_path: /tmp
      local_path: files/data
      state: to_pod

  - name: compare directories
    kubectl_file_compare:
      namespace: '{{ copy_namespace }}'
      pod: '{{ pod_with_one_container.name }}'
      remote_path: /tmp/data
      local_path: '{{ role_path }}/files/data'

  - name: copy directory from Pod into local filesystem (new directory to create)
    k8s_cp:
      namespace: '{{ copy_namespace }}'
      pod: '{{ pod_with_one_container.name }}'
      remote_path: /tmp/data
      local_path: /tmp/test
      state: from_pod

  - name: compare directories
    kubectl_file_compare:
      namespace: '{{ copy_namespace }}'
      pod: '{{ pod_with_one_container.name }}'
      remote_path: /tmp/data
      local_path: /tmp/test

  - name: copy directory from Pod into local filesystem (existing directory)
    k8s_cp:
      namespace: '{{ copy_namespace }}'
      pod: '{{ pod_with_one_container.name }}'
      remote_path: /tmp/data
      local_path: /tmp
      state: from_pod

  - name: compare directories
    kubectl_file_compare:
      namespace: '{{ copy_namespace }}'
      pod: '{{ pod_with_one_container.name }}'
      remote_path: /tmp/data
      local_path: /tmp/data

  # Test copy from Pod where the executable 'find' is missing
  - name: Ensure 'find' is missing from Pod
    k8s_exec:
      namespace: '{{ copy_namespace }}'
      pod: '{{ pod_without_executable_find.name }}'
      command: 'find'
    ignore_errors: true
    register: _result

  - name: Validate that 'find' executable is missing from Pod
    assert:
      that:
      - _result is failed
      fail_msg: "Pod contains 'find' executable, therefore we cannot run the next tasks."

  - name: Copy files into container
    k8s_cp:
      namespace: "{{ copy_namespace }}"
      pod: '{{ pod_without_executable_find.name }}'
      remote_path: '{{ item.path }}'
      content: '{{ item.content }}'
      state: to_pod
    with_items:
    - path: /ansible/root.txt
      content: this file is located at the root directory
    - path: /ansible/.hidden_root.txt
      content: this hidden file is located at the root directory
    - path: /ansible/.sudir/root.txt
      content: this file is located at the root of the sub directory
    - path: /ansible/.sudir/.hidden_root.txt
      content: this hidden file is located at the root of the sub directory

  - name: Delete existing directory
    file:
      path: /tmp/openjdk-files
      state: absent
    ignore_errors: true

  - name: copy directory from Pod into local filesystem (new directory to create)
    k8s_cp:
      namespace: '{{ copy_namespace }}'
      pod: '{{ pod_without_executable_find.name }}'
      remote_path: /ansible
      local_path: /tmp/openjdk-files
      state: from_pod

  - name: Compare directories
    kubectl_file_compare:
      namespace: '{{ copy_namespace }}'
      pod: '{{ pod_without_executable_find.name }}'
      remote_path: /ansible
      local_path: /tmp/openjdk-files

  always:
  - name: Remove directories created into remote Pod
    k8s_exec:
      namespace: '{{ copy_namespace }}'
      pod: '{{ pod_with_one_container.name }}'
      command: 'rm -rf {{ item }}'
    ignore_errors: true
    with_items:
    - /dest_data
    - /tmp/data

  - name: Remove local directories
    file:
      path: '{{ item }}'
      state: absent
    ignore_errors: true
    with_items:
    - /tmp/data
    - /tmp/test
    - /tmp/openjdk-files
