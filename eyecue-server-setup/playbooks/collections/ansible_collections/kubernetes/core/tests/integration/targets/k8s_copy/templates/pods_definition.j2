---
apiVersion: v1
kind: Pod
metadata:
  name: '{{ pod_with_one_container.name }}'
spec:
  containers:
  - name: '{{ pod_with_one_container.container }}'
    image: busybox
    command:
      - /bin/sh
      - -c
      - while true;do date;sleep 5; done
---
apiVersion: v1
kind: Pod
metadata:
  name: '{{ pod_with_two_container.name }}'
spec:
  containers:
  - name: '{{ pod_with_two_container.container[0] }}'
    image: busybox:1.32.0
    command:
      - /bin/sh
      - -c
      - while true;do date;sleep 5; done
  - name: '{{ pod_with_two_container.container[1] }}'
    image: busybox:1.33.0
    command:
      - /bin/sh
      - -c
      - while true;do date;sleep 5; done
---
apiVersion: v1
kind: Pod
metadata:
  name: '{{ pod_without_executable_find.name }}'
spec:
  containers:
  - name: openjdk17
    image: openjdk:17
    command:
    - /bin/sh
    - -c
    - while true;do date;sleep 5; done