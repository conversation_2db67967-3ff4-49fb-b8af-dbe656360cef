---
helm_default_archive_name: "helm-{{ helm_version }}-{{ ansible_system | lower }}-amd64.tar.gz"
helm_binary: "/tmp/helm/{{ ansible_system | lower }}-amd64/helm"

chart_test: "ingress-nginx"
chart_test_local_path: "nginx-ingress"
chart_test_version: 4.2.4
chart_test_version_local_path: 1.32.0
chart_test_version_upgrade: 4.2.5
chart_test_version_upgrade_local_path: 1.33.0
chart_test_repo: "https://kubernetes.github.io/ingress-nginx"
chart_test_git_repo: "http://github.com/helm/charts.git"
chart_test_values:
  revisionHistoryLimit: 0
  myValue: "changed"

test_namespace:
  - "helm-test-crds"
  - "helm-uninstall"
  - "helm-read-envvars"
  - "helm-dep-update"
  - "helm-local-path-001"
  - "helm-local-path-002"
  - "helm-local-path-003"
  - "helm-from-repository"
  - "helm-from-url"
