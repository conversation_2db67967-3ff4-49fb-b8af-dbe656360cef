---
- name: Get Information about All APIs
  k8s_cluster_info:
  register: api_details

- name: Print all APIs for debugging
  debug:
    msg: "{{ api_details.apis }}"

- name: Get core API version
  set_fact:
    crd: "{{ api_details.apis['apiextensions.k8s.io/v1'] }}"
    host: "{{ api_details.connection['host'] }}"
    client_version: "{{ api_details.version['client'] }}"

- name: Check if all APIs are present
  assert:
    that:
      - api_details.apis is defined
      - api_details.apis.v1.Secret is defined
      - api_details.apis.v1.Service is defined
      - crd is defined
      - host is defined
      - client_version is defined
