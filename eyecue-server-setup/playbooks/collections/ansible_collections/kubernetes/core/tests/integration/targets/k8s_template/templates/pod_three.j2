---
apiVersion: v1
kind: Pod
metadata:
  labels:
    app: "{{ k8s_pod_name_three_one }}"
  name: '{{ k8s_pod_name_three_one }}'
  namespace: '{{ k8s_pod_namespace }}'
spec:
  containers:
  - args:
    - /bin/sh
    - -c
    - while true; do echo $(date); sleep 10; done
    image: python:3.7-alpine
    imagePullPolicy: Always
    name: '{{ k8s_pod_name_three_one }}'

---
apiVersion: v1
kind: Pod
metadata:
  labels:
    app: "{{ k8s_pod_name_three_two }}"
  name: '{{ k8s_pod_name_three_two }}'
  namespace: '{{ k8s_pod_namespace }}'
spec:
  containers:
  - args:
    - /bin/sh
    - -c
    - while true; do echo $(date); sleep 10; done
    image: python:3.7-alpine
    imagePullPolicy: Always
    name: '{{ k8s_pod_name_three_two }}'
