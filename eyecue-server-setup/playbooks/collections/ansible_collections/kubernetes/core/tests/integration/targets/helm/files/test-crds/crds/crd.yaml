apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: foos.ansible.com
spec:
  group: ansible.com
  versions:
    - name: v1
      served: true
      storage: true
      schema:
        openAPIV3Schema:
          type: object
          properties:
            foobar:
              type: string
  scope: Namespaced
  names:
    plural: foos
    singular: foo
    kind: Foo
