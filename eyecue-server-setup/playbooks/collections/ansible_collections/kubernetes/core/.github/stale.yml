---
# Configuration for probot-stale - https://github.com/probot/stale

# Number of days of inactivity before an Issue or Pull Request becomes stale
daysUntilStale: 90

# Number of days of inactivity before an Issue or Pull Request with the stale
# label is closed. Set to false to disable. If disabled, issues still need to be
# closed manually, but will remain marked as stale.
daysUntilClose: 30

# Only issues or pull requests with all of these labels are check if stale.
# Defaults to `[]` (disabled)
onlyLabels: []

# Issues or Pull Requests with these labels will never be considered stale. Set
# to `[]` to disable
exemptLabels:
  - security
  - planned
  - priority/critical
  - lifecycle/frozen
  - verified

# Set to true to ignore issues in a project (defaults to false)
exemptProjects: false

# Set to true to ignore issues in a milestone (defaults to false)
exemptMilestones: true

# Set to true to ignore issues with an assignee (defaults to false)
exemptAssignees: false

# Label to use when marking as stale
staleLabel: lifecycle/stale

# Limit the number of actions per hour, from 1-30. Default is 30
limitPerRun: 30

pulls:
  markComment: |-
    PRs go stale after 90 days of inactivity.
    If there is no further activity, the PR will be closed in another 30 days.

  unmarkComment: >-
    This pull request is no longer stale.

  closeComment: >-
    This pull request has been closed due to inactivity.

issues:
  markComment: |-
    Issues go stale after 90 days of inactivity.
    If there is no further activity, the issue will be closed in another 30 days.

  unmarkComment: >-
    This issue is no longer stale.

  closeComment: >-
    This issue has been closed due to inactivity.
