---

# ansible-playbook -i tst_hosts.yml --vault-password-file vault-password.txt playbooks/infra_upgrade_and_setup.yml

- name: Master playbook to upgrade Ubuntu, reset Kubernetes, and install applications
  hosts: "{{ run_hosts.split(',') }}"

  gather_facts: true
  become: true

  collections:
    - kubernetes.core
    - community.docker
  vars:
    run_hosts: "localhost"
    delete_media_files: false
    reset_docker: false
    gather_facts: true
    customer: "{{ 'stb-nzl' if 'stb-nzl' in ansible_hostname else ansible_hostname[3:6] }}"
    site_contact_domain: ""
    enable_infra_logging: false
    enable_logging: false
    cleanup_prom_operator: false
    enable_wireguard_proxy: false
    kubeconfig_location: "/etc/kubernetes/admin.conf"

  tasks:
    - name: Print host name
      ansible.builtin.debug:
        msg: "RUNNING FOR HOST: {{ ansible_hostname }}"

    # Slack notification
    - name: Set fact for block file path
      ansible.builtin.set_fact:
        block_file_path: "/opt/.SLACK_NOTIFICATION_SENT"

    - name: Check if block file is in place
      ansible.builtin.stat:
        path: "{{ block_file_path }}"
      register: block_file

    - name: Debug block file stat
      ansible.builtin.debug:
        msg: "{{ block_file.stat }}"

    - name: Send Slack notification
      ansible.builtin.include_role:
        name: fm.slack_notification_ubuntu_upgrade

    - name: Create file to block next notifications
      ansible.builtin.file:
        path: "{{ block_file_path }}"
        state: touch
      when: not block_file.stat.exists

    # Tasks from eyecue_reset_k8s_cluster.yml
    - name: Remove /media content
      ansible.builtin.file:
        path: /media/fingermark/storage/*
        state: absent
      when: delete_media_files

    - name: Running kubeadm reset...
      ansible.builtin.command: "kubeadm reset --force"

    - name: Deleting kubernetes files and directories
      ansible.builtin.file:
        path: "{{ item }}"
        state: absent
      loop:
        - "/var/lib/etcd/"
        - "/etc/kubernetes/"
        - "/etc/cni/net.d/"
        - "/etc/kubernetes/kubelet.conf"
        - "/etc/kubernetes/pki/ca.crt"
        - "/etc/apt/sources.list.d/kubernetes.list"

    - name: "Removing Docker from the system"
      when: reset_docker
      block:
        - name: Prune everything
          community.docker.docker_prune:
            containers: true
            images: true
            networks: true
            volumes: true
            builder_cache: true
          tags:
            - docker

        - name: "Removing docker packages"
          ansible.builtin.apt:
            name:
              - docker-ce
              - docker-ce-cli
              - docker-ce-rootless-extras
            state: absent
            purge: true
            autoremove: true
          tags:
            - docker

        - name: Reboot host and wait for it to restart
          ansible.builtin.reboot:
            msg: "Reboot initiated by Ansible"
            connect_timeout: 5
            reboot_timeout: 600
            pre_reboot_delay: 0
            post_reboot_delay: 30
            test_command: whoami
          tags:
            - docker

        - name: Delete docker container files
          ansible.builtin.file:
            path: "{{ item }}"
            state: absent
          loop:
            - "/var/lib/docker"
            - "/etc/docker"
            - "/etc/apparmor.d/docker"
            - "/var/run/docker.sock"
          tags:
            - docker

        - name: Reinstall Docker
          ansible.builtin.include_role:
            name: geerlingguy.docker
          vars:
            docker_version: "5:20.10.14~3-0~ubuntu-{{ ansible_distribution_release | lower }}"
          tags:
            - docker

        - name: Installing Nvidia Container Runtime
          ansible.builtin.include_role:
            name: nvidia_gpu_operator
          tags:
            - nvidia
            - runtime
            - drivers

        - name: Setup nvidia-docker again
          ansible.builtin.include_role:
            name: fingermark.nvidia-docker
          tags:
            - nvidia
            - nvidia-docker

    # Tasks from ubuntu_upgrade.yml (prereq-ubuntu-upgrade and ubuntu-upgrade roles)
    # Upgrading from 18 to 20
    - name: Ensure needrestart package is updated
      apt:
        name: needrestart
        state: latest
        update_cache: yes

    - name: Milestone check
      ansible.builtin.debug:
        msg: "About to upgrade Ubuntu OS version from 18 to 20"

    - name: Upgrade all packages to the latest version
      apt: update_cache=yes upgrade=full
      ignore_errors: yes

    - name: Ensure update-manager-core is installed.
      apt: name=update-manager-core state=present
      ignore_errors: yes

    - name: Reboot host and wait for it to restart
      reboot:
        msg: "Reboot initiated by Ansible"
        connect_timeout: 5
        reboot_timeout: 600
        pre_reboot_delay: 0
        post_reboot_delay: 30
        test_command: whoami

    - name: Run do-release-upgrade non-interactively
      command: do-release-upgrade -f DistUpgradeViewNonInteractive
      ignore_errors: yes

    - name: Wait so that processes can finish elegantly
      wait_for:
        timeout: 300

    - name: Reboot host and wait for it to restart
      reboot:
        msg: "Reboot initiated by Ansible"
        connect_timeout: 5
        reboot_timeout: 600
        pre_reboot_delay: 0
        post_reboot_delay: 30
        test_command: whoami

    # Tasks from ubuntu_upgrade.yml (prereq-ubuntu-upgrade and ubuntu-upgrade roles)
    # Upgrading from 20 to 22
    - name: Milestone check
      ansible.builtin.debug:
        msg: "About to upgrade Ubuntu OS version from 20 to 22"

    - name: Upgrade all packages to the latest version
      apt: update_cache=yes upgrade=full
      ignore_errors: yes

    - name: Ensure update-manager-core is installed.
      apt: name=update-manager-core state=present
      ignore_errors: yes

    - name: Reboot host and wait for it to restart
      reboot:
        msg: "Reboot initiated by Ansible"
        connect_timeout: 5
        reboot_timeout: 600
        pre_reboot_delay: 0
        post_reboot_delay: 30
        test_command: whoami

    - name: Run do-release-upgrade non-interactively
      command: do-release-upgrade -f DistUpgradeViewNonInteractive
      ignore_errors: yes

    - name: Reboot host and wait for it to restart
      reboot:
        msg: "Reboot initiated by Ansible"
        connect_timeout: 5
        reboot_timeout: 600
        pre_reboot_delay: 0
        post_reboot_delay: 30
        test_command: whoami

    # Tasks to Install Kubernetes
    - name: Milestone check
      ansible.builtin.debug:
        msg: "About to install and configure Kubernetes"

    - name: Install required Python packages
      pip:
        name:
          - kubernetes
        state: present

    - name: Install Kubernetes
      block:
      - name: Installing kubeadm, kubectl and kubelet
        ansible.builtin.import_role:
          name: kubernetes/master
        vars:
          kube_version: "v1.20.0"
          kubeadm_opts: "--apiserver-advertise-address '*************'"
        tags:
          - k8s
          - kubernetes
      rescue:
      - name: Reboot host after failure
        ansible.builtin.reboot:
          msg: "Reboot initiated by Ansible due to failure"
          connect_timeout: 5
          reboot_timeout: 600
          pre_reboot_delay: 0
          post_reboot_delay: 30
          test_command: whoami

      - name: Wait so that processes can finish elegantly
        wait_for:
          timeout: 300

      - name: Installing kubeadm, kubectl and kubelet
        ansible.builtin.import_role:
          name: kubernetes/master
        vars:
          kube_version: "v1.20.0"
          kubeadm_opts: "--apiserver-advertise-address '*************'"
        tags:
          - k8s
          - kubernetes

    - name: Install Calico
      block:
      - name: Installing Calico
        ansible.builtin.import_role:
          name: calico
        vars:
          calico_manifest: /tmp/calico.yaml
          pod_network_cidr: **********/16
        tags:
          - k8s
          - kubernetes
          - calico

      rescue:
      - name: Reboot host after failure
        ansible.builtin.reboot:
          msg: "Reboot initiated by Ansible due to failure"
          connect_timeout: 5
          reboot_timeout: 600
          pre_reboot_delay: 0
          post_reboot_delay: 30
          test_command: whoami

      - name: Wait so that processes can finish elegantly
        wait_for:
          timeout: 600

      - name: Installing Calico
        ansible.builtin.import_role:
          name: calico
        vars:
          calico_manifest: /tmp/calico.yaml
          pod_network_cidr: **********/16
        tags:
          - k8s
          - kubernetes
          - calico

    - name: Reboot after installing Kubernetes
      reboot:
        msg: "Rebooting after installing Kubernetes"
        connect_timeout: 5
        reboot_timeout: 600
        pre_reboot_delay: 0
        post_reboot_delay: 30
        test_command: whoami
      tags:
        - reboot

    - name: Wait so that processes can finish elegantly
      wait_for:
        timeout: 600

    # Tasks to Install Monitoring and Eyecue
    - name: Milestone check
      ansible.builtin.debug:
        msg: "About to install applications"

    - name: Install Prometheus
      block:
      - name: Installing Prometheus
        ansible.builtin.import_role:
          name: fingermark.kube-monitoring
        vars:
          kube_version: "v1.20.0"
          thanos_upgrade: false
          thanos_sidecar_install: true
          node_exporter_standalone_version: false
          monitoring_install_logging: true
          prometheus_operator_uninstall: "{{ cleanup_prom_operator }}"
        tags:
          - monitoring
          - k8s
          - application
          - prometheus
          - kube-monitoring

      rescue:
      - name: Reboot after Prometheus error
        reboot:
          msg: "Reboot after Prometheus error"
          connect_timeout: 5
          reboot_timeout: 600
          pre_reboot_delay: 0
          post_reboot_delay: 30
          test_command: whoami
        tags:
          - reboot

      - name: Wait so that processes can finish elegantly
        wait_for:
          timeout: 600

      - name: Installing Prometheus
        ansible.builtin.import_role:
          name: fingermark.kube-monitoring
        vars:
          kube_version: "v1.20.0"
          thanos_upgrade: false
          thanos_sidecar_install: true
          node_exporter_standalone_version: false
          monitoring_install_logging: true
          prometheus_operator_uninstall: "{{ cleanup_prom_operator }}"
        tags:
          - monitoring
          - k8s
          - application
          - prometheus
          - kube-monitoring

    - name: Install Eyecue applications
      block:
      - name: Ensure DNS is configured correctly
        lineinfile:
          path: /etc/systemd/resolved.conf
          regexp: '^#?FallbackDNS='
          line: 'FallbackDNS=8.8.8.8 8.8.4.4'
        notify: Restart systemd-resolved

      - name: Ensure systemd-resolved is enabled and running
        systemd:
          name: systemd-resolved
          state: started
          enabled: yes

      - ansible.builtin.import_role:
          name: argocd
        vars:
          delete_eyecue_app: no
          remove_argocd_ns: no
          argocd_bin_update: no
          destination_manifest: /opt/argocd/
          argocd_bitbucket_repo: "*****************:fingermarkltd/eyecue-{{ customer if customer != 'mcd' else 'mcdonalds' | lower }}-helm"
        tags:
          - eyecue
          - argocd
          - application

      rescue:
      - name: Reboot after Bitbucket server error
        reboot:
          msg: "Reboot after Bitbucket server error"
          connect_timeout: 5
          reboot_timeout: 600
          pre_reboot_delay: 0
          post_reboot_delay: 30
          test_command: whoami
        tags:
          - reboot

      - name: Wait so that processes can finish elegantly
        wait_for:
          timeout: 600

      # Error handling for bitbucket server misbehaving (second try)
      - name: Ensure DNS is configured correctly
        lineinfile:
          path: /etc/systemd/resolved.conf
          regexp: '^#?FallbackDNS='
          line: 'FallbackDNS=8.8.8.8 8.8.4.4'
        notify: Restart systemd-resolved

      - name: Ensure systemd-resolved is enabled and running
        systemd:
          name: systemd-resolved
          state: started
          enabled: yes

      - ansible.builtin.import_role:
          name: argocd
        vars:
          delete_eyecue_app: no
          remove_argocd_ns: no
          argocd_bin_update: no
          destination_manifest: /opt/argocd/
          argocd_bitbucket_repo: "*****************:fingermarkltd/eyecue-{{ customer if customer != 'mcd' else 'mcdonalds' | lower }}-helm"
        tags:
          - eyecue
          - argocd
          - application

      rescue:
      - name: Reboot after Bitbucket server error
        reboot:
          msg: "Reboot after Bitbucket server error"
          connect_timeout: 5
          reboot_timeout: 600
          pre_reboot_delay: 0
          post_reboot_delay: 30
          test_command: whoami
        tags:
          - reboot

      - name: Wait so that processes can finish elegantly
        wait_for:
          timeout: 600

      # Error handling for bitbucket server misbehaving
      - name: Ensure DNS is configured correctly
        lineinfile:
          path: /etc/systemd/resolved.conf
          regexp: '^#?FallbackDNS='
          line: 'FallbackDNS=8.8.8.8 8.8.4.4'
        notify: Restart systemd-resolved

      - name: Ensure systemd-resolved is enabled and running
        systemd:
          name: systemd-resolved
          state: started
          enabled: yes

      - ansible.builtin.import_role:
          name: argocd
        vars:
          delete_eyecue_app: no
          remove_argocd_ns: no
          argocd_bin_update: no
          destination_manifest: /opt/argocd/
          argocd_bitbucket_repo: "*****************:fingermarkltd/eyecue-{{ customer if customer != 'mcd' else 'mcdonalds' | lower }}-helm"
        tags:
          - eyecue
          - argocd
          - application

  handlers:
    - name: Restart systemd-resolved
      service:
        name: systemd-resolved
        state: restarted
