---
    # To use this role, add --ask-vault-pass on the ansible-playbook command
   - name: Fingermark Computer Vision Server
   
     # Note: Please use ./bin/playbook-run.sh (prompts for inputs OR use the below)
     # i.e. ./bin/playbook-run.sh "jeremy" "06_onsite_setup_server.yaml" "dev_hosts.yml" "fm-tst-nz-4242"
     hosts: "{{ run_hosts | split(',') | default('localhost')}}"
   
     become: true
     vars:
       # k3s_setup_task: false
       calico_manifest: https://raw.githubusercontent.com/projectcalico/calico/v3.25.0/manifests/calico.yaml
       kubeconfig_location: "/etc/rancher/k3s/k3s.yaml"
     roles:
       - role: timezone
         tags: 
           - basics
   
       - role: netplan_setup
         tags: 
           - network
         vars:
           netplan_server_ip: "***********/24"
           netplan_default_gateway: "***********"
           netplan_apply_new_config: yes
           netplan_config_static: false
   
       - role: fm.configure_containerd
         tags:
           - "containerd"
         vars:
           microk8s_cache_valid_time: 3600
   
       - role: k3s
         tags: 
           - k8s
           - k3s
   
       - role: calico
         tags: 
           - k8s
           - calico
         vars:
           calico_manifest: /tmp/calico.yaml
           kubeadmin_config: "/etc/rancher/k3s/k3s.yaml"
   
       - role: nvidia_gpu_operator
         tags: 
           - k8s
           - nvidia
   
       - role: argocd
         tags: 
           - application
           - argocd
         vars:
           delete_eyecue_app: no
           remove_argocd_ns: no
           argocd_bin_update: no
           destination_manifest: /opt/argocd/
           kubectl: "/usr/local/bin/kubectl --kubeconfig {{ kubeconfig_location }}"
   
       - role: fm.icinga2
         tags: 
           - monitoring
           - icinga2
         vars:
           icinga2_node_setup: yes
           icinga_force_setup: no
           icinga2_deploy_cameras_check: yes
           icinga2_deploy_serialnumber_check: yes
           icinga2_deploy_kubernetes_check: yes
   
       - role: fingermark.kube-monitoring
         tags: 
           - monitoring
           - k8s
           - application
         vars:
           thanos_upgrade: no
           thanos_sidecar_install: yes
           node_exporter_standalone_version: no  
           # LOGGING
           monitoring_install_logging: yes
           delete_logging_ns: no
           
       - role: geerlingguy.security
         tags: 
           - security
   
       - role: fm.ubuntu-hardening
         tags: 
           - security
   
   
     tasks:
       - name: Get cameras for the first time
         command: /bin/bash /usr/local/bin/get_cameras -y
         when: icinga2_deploy_cameras_check
   