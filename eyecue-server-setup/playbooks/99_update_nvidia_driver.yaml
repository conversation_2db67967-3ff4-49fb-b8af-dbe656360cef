---
- name: "Update nvidia driver and dependencies"
  hosts: "{{ run_hosts | split(',') | default('localhost')}}" 
  gather_facts: true
  become: true
  vars:
    ansible_python_interpreter: /usr/bin/python3.10

  pre_tasks:

    - name: Setting DISPLAY_NAME env var
      ansible.builtin.lineinfile:
        line: DISPLAY_NAME="{{ display_name }}"
        path: /etc/environment
        regexp: DISPLAY_NAME

    - name: Ensure apt signing key is present
      ansible.builtin.apt_key:
        url: https://packages.cloud.google.com/apt/doc/apt-key.gpg
        state: present

    - name: Remove leftovers from other installs
      file: 
        path: /etc/apt/sources.list.d/nvidia-container-toolkit.list
        state: absent

    - name: Fix broken packages
      ansible.builtin.apt:
        state: fixed

    - name: Configure dpkg
      ansible.builtin.shell: |
        dpkg --configure -a
      args:
        executable: /bin/bash

      # apt doesnt provide regex
    - name: remove old nvidia packages and dependencies
      ansible.builtin.shell: |
        apt-get -y remove --purge '^nvidia-.*'
      args:
        executable: /bin/bash

    - name: Remove dependencies that are no longer required
      ansible.builtin.apt:
        autoremove: yes

    - name: Remove useless packages from the cache
      ansible.builtin.apt:
        autoclean: yes

    - name: Delete content & directory
      file:
        state: absent
        path:
          - /etc/sensors.d/.placeholder
          - /etc/sensors3.conf
      ignore_errors: yes

    - name: Update libsensors
      ansible.builtin.apt:
        pkg:
          - libsensors5
        update_cache: yes
      ignore_errors: yes

  roles:
    - role: "fingermark.nvidia-driver"
      tags:
        - "nvidia"
      vars:
        nvidia_driver_use_ppa: "ppa:graphics-drivers/ppa"
        nvidia_driver_skip_reboot: true # We reboot manually to save time

    - role: "geerlingguy.docker"
      tags:
        - "docker"
      vars:
        docker_version: "5:20.10.14~3-0~ubuntu-{{ ansible_distribution_release | lower }}"

    - role: "fingermark.nvidia-docker"
      tags: 
        - nvidia
        - nvidia-docker
      vars:
        nvidia_driver_skip_reboot: true # We reboot manually to save time

  tasks:
    - name: Reboot host and wait for it to restart
      reboot:
        msg: "Reboot initiated by Ansible"
        connect_timeout: 5
        reboot_timeout: 600
        pre_reboot_delay: 0
        post_reboot_delay: 60
        test_command: whoami
